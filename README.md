# 金融股指期货监控数据面板

一个基于Python的金融股指期货监控系统，通过实时数据计算基差贴水升水的年化平均成本，以及不同月份合约之间的价差时间成本，形成成本矩阵监控。

## 🎯 项目目标

- 📊 **实时监控**：股指期货与现货指数的基差变化
- 💰 **成本计算**：年化基差成本和时间成本分析
- 📈 **数据可视化**：清晰的成本矩阵展示
- ⚡ **自动更新**：持续监控市场变化

## 🏗️ 系统架构

```
futures_monitor/
├── src/                          # 源代码目录
│   ├── models/                   # 数据模型
│   │   └── data_models.py       # 期货、指数、基差数据模型
│   ├── data_providers/          # 数据提供者
│   │   ├── efinance_provider.py # efinance API数据源
│   │   └── index_provider.py    # 指数数据提供者
│   ├── utils/                   # 工具模块
│   │   └── trading_calendar.py  # 交易日历计算
│   ├── calculators/             # 计算器模块
│   │   ├── basis_calculator.py  # 基差计算器
│   │   └── spread_calculator.py # 价差计算器
│   ├── analyzers/               # 分析器模块
│   │   └── cost_matrix.py       # 成本矩阵生成器
│   └── core/                    # 核心架构
│       └── architecture.py      # 系统架构设计
├── docs/                        # 文档目录
├── main.py                      # 完整版主程序
├── simple_monitor.py            # 简化版演示程序
└── README.md                    # 项目说明
```

## 📊 监控品种

### 股指期货品种
- **IC系列**：中证500股指期货
- **IM系列**：中证1000股指期货
- **IF系列**：沪深300股指期货
- **IH系列**：上证50股指期货

### 对应现货指数
- **沪深300指数**：sh000300
- **中证500指数**：sh000905
- **中证1000指数**：sh000852
- **上证50指数**：sh000016

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install efinance pandas numpy asyncio

# 克隆项目
git clone <repository_url>
cd futures_monitor
```

### 2. 运行演示

```bash
# 运行简化版监控系统
python simple_monitor.py
```

### 3. 运行完整版

```bash
# 运行完整版监控系统（需要稳定网络）
python main.py
```

## 📈 核心功能

### 1. 基差计算
- **基差** = 期货价格 - 现货价格
- **基差率** = (基差 / 现货价格) × 100%
- **年化成本** = (基差率 / 剩余天数) × 年交易天数

### 2. 价差分析
- **价差** = 远月合约价格 - 近月合约价格
- **价差率** = (价差 / 近月价格) × 100%
- **年化时间成本** = (价差率 / 时间差天数) × 年交易天数

### 3. 成本矩阵
- 基差成本矩阵：各品种期货与现货的成本分析
- 价差成本矩阵：不同月份合约间的时间成本
- 综合成本矩阵：整体监控面板

## 📊 输出示例

```
================================================================================
📊 基差成本监控面板
================================================================================

【基差成本矩阵】
品种   合约代码 合约月份 是否主力   期货价格    现货价格     基差  基差率(%)  剩余天数  年化成本(%)
IC   060130   0130    是      5865.4   5865.40   0.00    0.00     0     0.00
IC   061107   1107    否      5871.0   5865.40   5.60    0.10    10     2.32
IF   040130   0130    是      3962.0   3960.12   1.88    0.05     0     0.00
IF   041107   1107    否      3975.0   3960.12  14.88    0.38    10     9.13

【汇总统计】
监控合约数量: 8
平均基差率: 0.40%
平均年化成本: 9.65%
最大年化成本: 61.56%
```

## 🔧 配置说明

### 监控配置 (MonitoringConfig)
```python
config = MonitoringConfig(
    futures_mapping={
        'IC': 'sh000905',  # 中证500股指期货 -> 中证500指数
        'IM': 'sh000852',  # 中证1000股指期货 -> 中证1000指数
        'IF': 'sh000300',  # 沪深300股指期货 -> 沪深300指数
        'IH': 'sh000016',  # 上证50股指期货 -> 上证50指数
    },
    trading_days_per_year=243,    # 每年交易日天数
    update_interval=60,           # 数据更新间隔（秒）
    log_level="INFO"             # 日志级别
)
```

## 📚 API文档

### 数据源
- **efinance**：期货行情数据、部分指数数据
- **adata**：备用指数数据源（可选）

### 主要类
- `FuturesContract`：期货合约信息
- `FuturesQuote`：期货行情数据
- `IndexQuote`：指数行情数据
- `BasisData`：基差计算结果
- `SpreadData`：价差计算结果
- `CostMatrix`：成本矩阵

## ⚠️ 注意事项

1. **网络依赖**：系统需要稳定的网络连接获取实时数据
2. **数据准确性**：efinance的部分指数数据可能不准确，建议验证
3. **交易时间**：建议在交易时间内运行以获取最新数据
4. **风险提示**：本系统仅供参考，投资决策请谨慎

## 🔮 未来扩展

- [ ] Web界面展示
- [ ] 数据持久化存储
- [ ] 实时WebSocket推送
- [ ] 更多技术指标
- [ ] 套利机会识别
- [ ] 风险预警系统

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

**免责声明**：本项目仅供学习和研究使用，不构成投资建议。投资有风险，入市需谨慎。
