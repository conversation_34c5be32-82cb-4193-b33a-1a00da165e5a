#!/usr/bin/env python3
"""
创建模拟的期货基础信息缓存文件
用于测试缓存功能
"""

import os
import pickle
import pandas as pd
from datetime import datetime

def create_mock_futures_base_info():
    """创建模拟的期货基础信息"""
    data = [
        # IC 中证500
        {'期货代码': 'IC2501', '期货名称': 'IC2501', '最新价': 5865.4, '成交量': 11617, '成交额': 6.8e10},
        {'期货代码': 'IC2503', '期货名称': 'IC2503', '最新价': 5868.2, '成交量': 8500, '成交额': 5.0e10},
        {'期货代码': 'IC2506', '期货名称': 'IC2506', '最新价': 5870.1, '成交量': 6200, '成交额': 3.6e10},
        {'期货代码': 'IC2509', '期货名称': 'IC2509', '最新价': 5871.0, '成交量': 4100, '成交额': 2.4e10},
        
        # IM 中证1000
        {'期货代码': 'IM2501', '期货名称': 'IM2501', '最新价': 6118.2, '成交量': 41209, '成交额': 2.5e11},
        {'期货代码': 'IM2503', '期货名称': 'IM2503', '最新价': 6150.5, '成交量': 25000, '成交额': 1.5e11},
        {'期货代码': 'IM2506', '期货名称': 'IM2506', '最新价': 6200.8, '成交量': 18000, '成交额': 1.1e11},
        {'期货代码': 'IM2509', '期货名称': 'IM2509', '最新价': 6273.2, '成交量': 12000, '成交额': 7.5e10},
        
        # IF 沪深300
        {'期货代码': 'IF2501', '期货名称': 'IF2501', '最新价': 3962.0, '成交量': 25000, '成交额': 9.9e10},
        {'期货代码': 'IF2503', '期货名称': 'IF2503', '最新价': 3968.5, '成交量': 18000, '成交额': 7.1e10},
        {'期货代码': 'IF2506', '期货名称': 'IF2506', '最新价': 3972.2, '成交量': 12000, '成交额': 4.8e10},
        {'期货代码': 'IF2509', '期货名称': 'IF2509', '最新价': 3975.0, '成交量': 8000, '成交额': 3.2e10},
        
        # IH 上证50
        {'期货代码': 'IH2501', '期货名称': 'IH2501', '最新价': 2890.0, '成交量': 18000, '成交额': 5.2e10},
        {'期货代码': 'IH2503', '期货名称': 'IH2503', '最新价': 2892.5, '成交量': 12000, '成交额': 3.5e10},
        {'期货代码': 'IH2506', '期货名称': 'IH2506', '最新价': 2894.2, '成交量': 8000, '成交额': 2.3e10},
        {'期货代码': 'IH2509', '期货名称': 'IH2509', '最新价': 2895.0, '成交量': 5000, '成交额': 1.4e10},
    ]
    
    return pd.DataFrame(data)

def main():
    """主函数"""
    # 确保缓存目录存在
    cache_dir = "cache"
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir)
    
    # 创建模拟数据
    futures_data = create_mock_futures_base_info()
    
    # 准备缓存数据
    cache_data = {
        'data': futures_data,
        'timestamp': datetime.now(),
        'version': '1.0'
    }
    
    # 保存到缓存文件
    cache_file = os.path.join(cache_dir, "futures_base_info.pkl")
    
    with open(cache_file, 'wb') as f:
        pickle.dump(cache_data, f)
    
    print(f"✅ 模拟期货基础信息缓存已创建: {cache_file}")
    print(f"📊 包含 {len(futures_data)} 个期货合约")
    print("\n期货合约列表:")
    for _, row in futures_data.iterrows():
        print(f"  {row['期货代码']} - {row['期货名称']} - 价格: {row['最新价']}")

if __name__ == "__main__":
    main()
