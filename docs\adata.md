adata数据字典:

https://adata.30006124.xyz/dic/dataList.html

### 40.3 实时

#### 说明介绍

获取当个指数的实时行情，即最新的指数行情数据。

#### 调用方法

```python
stock.market.get_market_index_current()
```

**Copied!**

#### 输入参数

| 参数       | 类型   | 是否必填 | 说明                          |
| ---------- | ------ | -------- | ----------------------------- |
| index_code | string | 是       | 指数代码；例：000001 上证指数 |

#### 返回结果

| 字段       | 类型    | 注释       | 说明                                |
| ---------- | ------- | ---------- | ----------------------------------- |
| index_code | string  | 代码       | 000001                              |
| trade_time | time    | 交易时间   | 1990-01-01 00:00:00；返回当前的时间 |
| trade_date | date    | 交易日期   | 1990-01-01                          |
| open       | decimal | 开盘价     | 9.98                                |
| price      | decimal | 现价       | 9.98                                |
| high       | decimal | 最高价     | 9.98                                |
| low        | decimal | 最低价     | 9.98                                |
| volume     | decimal | 成交量(股) | 64745722                            |
| amount     | decimal | 成交额(元) | 934285179.00                        |

#### 参考示例

```python
import adata
df = adata.stock.market.get_market_index_current(index_code='000001')
print(df)
```

**Copied!**

```python
# 结果示例
            trade_time  trade_date  ...           amount index_code
0  2023-07-06 18:07:00  2023-07-06  ...  320808310000.00     000001

[1 rows x 9 columns]
```





## 91. 交易日历

**说明介绍**

获取对应年份的交易日历信息

**调用方法**

```python
stock.info.trade_calendar()
```

**Copied!**

**Copied!**

**输入参数**

| 参数 | 类型 | 是否必填 | 说明           |
| ---- | ---- | -------- | -------------- |
| year | int  | 是       | 年份；例：2023 |

**返回结果**

| 字段         | 类型 | 注释                           | 说明           |
| ------------ | ---- | ------------------------------ | -------------- |
| trade_date   | date | 交易日                         | 2023-05-20     |
| trade_status | int  | 交易状态：0.非交易日；1.交易日 | 1              |
| day_week     | int  | 一周第几天                     | 从星期天开始的 |

**参考示例**

```python
import adata
df = adata.stock.info.trade_calendar(year=2023)
print(df)
```

**Copied!**

**Copied!**

```python
# 结果示例
     trade_date trade_status  day_week
0    2023-01-01            0         1
1    2023-01-02            0         2
2    2023-01-03            1         3
3    2023-01-04            1         4
4    2023-01-05            1         5
..          ...          ...       ...
360  2023-12-27            1         4
361  2023-12-28            1         5
362  2023-12-29            1         6
363  2023-12-30            0         7
364  2023-12-31            0         1

[365 rows x 3 columns]
```





## 51. 指数代码信息

**说明介绍**

获取A股所有指数信息列表

**调用方法**

```python
stock.info.all_index_code()
```

**Copied!**

**Copied!**

**输入参数**

无

**返回结果**

| 字段         | 类型   | 注释     | 说明                                |
| ------------ | ------ | -------- | ----------------------------------- |
| name         | string | 指数简称 | 能源金属                            |
| index_code   | string | 指数代码 | 399366                              |
| concept_code | string | 概念代码 | 同花顺的编码；例：000819 对应1B0819 |
| source       | string | 来源     | 同花顺                              |

**参考示例**

```python
import adata
df = adata.stock.info.all_index_code()
print(df)
```

**Copied!**

**Copied!**

```python
# 结果示例
    index_code concept_code   name source
0       399366       399366   能源金属    同花顺
1       000823       1B0823  800有色    同花顺
2       399395       399395   国证有色    同花顺
3       000819       1B0819   有色金属    同花顺
4       399232       399232   采矿指数    同花顺
..         ...          ...    ...    ...
546     399617       399617   深证消费    同花顺
547     399389       399389   国证通信    同花顺
548     000869       1B0869   HK银行    同花顺
549     399621       399621   深证电信    同花顺
550     399688       399688   深成电信    同花顺

[551 rows x 4 columns]
```
