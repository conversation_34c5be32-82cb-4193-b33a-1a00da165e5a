#### 中国金融期货交易所[](https://akshare.akfamily.xyz/data/futures/futures.html#id52 "Link to this heading")

接口: futures_contract_info_cffex

目标地址: http://www.gfex.com.cn/gfex/hyxx/ywcs.shtml

描述: 中国金融期货交易所-数据-交易参数

限量: 单次返回指定 date 的期货合约信息数据

输入参数

| 名称 | 类型 | 描述                    |
| ---- | ---- | ----------------------- |
| date | str  | date="20240228"; 交易日 |

输出参数

| 名称       | 类型    | 描述 |
| ---------- | ------- | ---- |
| 合约代码   | object  | -    |
| 合约月份   | object  | -    |
| 挂盘基准价 | float64 | -    |
| 上市日     | object  | -    |
| 最后交易日 | object  | -    |
| 涨停板幅度 | object  | -    |
| 跌停板幅度 | object  | -    |
| 涨停板价位 | float64 | -    |
| 跌停板价位 | float64 | -    |
| 持仓限额   | int64   | -    |
| 品种       | object  | -    |
| 查询交易日 | object  | -    |

接口示例

```
importakshareasak

futures_contract_info_cffex_df = ak.futures_contract_info_cffex(date="20240228")
print(futures_contract_info_cffex_df)
```

数据示例

```
        合约代码    合约月份    挂盘基准价 上市日  ...  跌停板价位 持仓限额 品种  查询交易日
0           TS2409  2409  100.974  2023-12-11  ...  101.056  2000  TS  2024-02-28
1           TS2406  2406  101.065  2023-09-11  ...  101.020  2000  TS  2024-02-28
2           TS2403  2403  101.145  2023-06-12  ...  100.858  2000  TS  2024-02-28
3           TL2409  2409   99.330  2023-12-11  ...  102.990  2000  TL  2024-02-28
4           TL2406  2406   98.750  2023-09-11  ...  102.870  2000  TL  2024-02-28
..             ...   ...      ...         ...  ...      ...   ...  ..         ...
831  HO2403-C-2075  2403  193.200  2023-12-18  ...   83.800  1200  HO  2024-02-28
832  HO2403-C-2050  2403  253.400  2023-12-06  ...  115.000  1200  HO  2024-02-28
833  HO2403-C-2025  2403  234.800  2023-12-18  ...  133.600  1200  HO  2024-02-28
834  HO2403-C-2000  2403  286.200  2023-12-08  ...  158.600  1200  HO  2024-02-28
835  HO2403-C-1975  2403  241.200  2024-01-18  ...  189.600  1200  HO  2024-02-28
[836 rows x 12 columns]
```



## 期货行情数据[](https://akshare.akfamily.xyz/data/futures/futures.html#id53 "Link to this heading")

### 内盘-实时行情数据[](https://akshare.akfamily.xyz/data/futures/futures.html#id54 "Link to this heading")

接口: futures_zh_spot

目标地址: https://finance.sina.com.cn/futuremarket/

描述: 新浪财经-期货页面的实时行情数据

限量: 单次返回当日可以订阅的所有期货品种数据

输入参数

| 名称           | 类型 | 描述                                                                             |
| -------------- | ---- | -------------------------------------------------------------------------------- |
| subscribe_list | str  | 需要订阅的合约代码; e.g., 按照示例获取                                           |
| market         | str  | market="CF"; market="CF": 商品期货, market="FF": 金融期货                        |
| adjust         | str  | adjust='0'; adjust='1': 返回合约、交易所和最小变动单位的实时数据, 返回数据会变慢 |

输出参数

| 名称              | 类型    | 描述                                   |
| ----------------- | ------- | -------------------------------------- |
| symbol            | object  | 品种                                   |
| time              | object  | 时间, e.g., 144050表示下午14点40分50秒 |
| open              | float64 | 开盘                                   |
| high              | float64 | 高                                     |
| low               | float64 | 低                                     |
| current_price     | float64 | 当前价格(买价)                         |
| bid_price         | float64 | 买                                     |
| ask_price         | float64 | 卖价                                   |
| buy_vol           | int64   | 买量                                   |
| sell_vol          | int64   | 卖量                                   |
| hold              | float64 | 持仓量                                 |
| volume            | int64   | 成交量                                 |
| avg_price         | float64 | 均价                                   |
| last_close        | float64 | 上一个交易日的收盘价                   |
| last_settle_price | float64 | 上一个交易日的结算价                   |

接口示例-单品种获取

```
importakshareasak

futures_zh_spot_df = ak.futures_zh_spot(symbol='V2205', market="CF", adjust='0')
print(futures_zh_spot_df)
```

数据示例-单品种获取

```
    symbol    time    open  ...  avg_price  last_close  last_settle_price
0  PVC2205  151039  8280.0  ...     8449.0      8423.0             8397.0
```

接口示例-多品种获取

```
importakshareasak

# 此处的合约需要是近期的合约, 否则会报错
futures_zh_spot_df = ak.futures_zh_spot(symbol='V2205, P2205, B2201, M2205', market="CF", adjust='0')
print(futures_zh_spot_df)
```

数据示例-多品种获取

```
    symbol    time    open  ...  avg_price  last_close  last_settle_price
0  PVC2205  151039  8280.0  ...     8449.0      8423.0             8397.0
1  棕榈油2205  151039  7690.0  ...     7844.0      7926.0             7848.0
2   豆二2201  151039  4165.0  ...     4193.0      4203.0             4203.0
3   豆粕2205  151039  3151.0  ...     3164.0      3153.0             3159.0
```

接口示例-订阅所有商品期货(大商所, 上期所, 郑商所主力合约)

```
importtime
importakshareasak

dce_text = ak.match_main_contract(symbol="dce")
czce_text = ak.match_main_contract(symbol="czce")
shfe_text = ak.match_main_contract(symbol="shfe")
gfex_text = ak.match_main_contract(symbol="gfex")

while True:
    time.sleep(3)
    futures_zh_spot_df = ak.futures_zh_spot(
        symbol=",".join([dce_text, czce_text, shfe_text, gfex_text]),
        market="CF",
        adjust='0')
    print(futures_zh_spot_df)
```

数据示例-商品期货

```
        symbol    time       open  ...  avg_price  last_close  last_settle_price
0     PVC2309  150118    5994.00  ...    6009.00     6025.00            6006.00
1     棕榈油2309  150118    6850.00  ...    6842.00     6846.00            6960.00
2      豆二2306  150118    3926.00  ...    3976.00     3999.00            3979.00
3      豆粕2309  150118    3441.00  ...    3478.00     3497.00            3457.00
4     铁矿石2309  150919     712.00  ...     715.00      714.00             721.00
5      鸡蛋2309  150118    4216.00  ...    4194.00     4187.00            4216.00
6      塑料2309  150118    8050.00  ...    8046.00     8039.00            8124.00
7     聚丙烯2309  150118    7415.00  ...    7427.00     7424.00            7471.00
8     纤维板2305  150118    1146.50  ...    1144.00     1158.00            1158.00
9      豆油2309  150118    7452.00  ...    7470.00     7468.00            7558.00
10     玉米2307  150919    2660.00  ...    2643.00     2644.00            2673.00
11     豆一2307  150118    4849.00  ...    4822.00     4837.00            4878.00
12     焦炭2309  150118    2160.00  ...    2147.00     2149.00            2194.50
13     焦煤2309  150118    1408.00  ...    1384.50     1385.00            1439.50
14     淀粉2307  150118    2983.00  ...    2964.00     2973.00            2993.00
15    乙二醇2309  150118    4156.00  ...    4172.00     4162.00            4197.00
16     粳米2307  150118    3432.00  ...    3427.00     3419.00            3438.00
17    苯乙烯2306  150118    8230.00  ...    8240.00     8251.00            8285.00
18     生猪2307  150118   16375.00  ...   16480.00    16520.00           16300.00
19    PTA2309  145959    5586.00  ...    5590.00     5620.00            5640.00
20     菜油2309  145959    8041.00  ...    8078.00     8047.00            8108.00
21     菜籽2311  145944    5536.00  ...    5470.00     5503.00            5476.00
22     菜粕2309  145959    2920.00  ...    2964.00     2998.00            2928.00
26     白糖2307  145959    7007.00  ...    6981.00     7035.00            6903.00
27     棉花2309  145959   15285.00  ...   15370.00    15485.00           15275.00
28     甲醇2309  145959    2332.00  ...    2337.00     2334.00            2375.00
29     玻璃2309  145959    1753.00  ...    1763.00     1793.00            1779.00
30     硅铁2306  145959    7340.00  ...    7312.00     7314.00            7350.00
31     锰硅2306  145959    7150.00  ...    7150.00     7170.00            6976.00
32     棉纱2309  145959   22380.00  ...   22565.00    22625.00           22455.00
33     苹果2310  145959    8546.00  ...    8573.00     8622.00            8513.00
34     红枣2309  145959   10225.00  ...   10490.00    10645.00           10255.00
35     尿素2309  145959    1989.00  ...    1997.00     1994.00            1989.00
36     纯碱2309  145959    2141.00  ...    2120.00     2094.00            2147.00
37     短纤2306  145959    7304.00  ...    7318.00     7364.00            7382.00
38     花生2310  145959   10510.00  ...   10612.00    10680.00           10526.00
39    燃料油2309  150000    2944.00  ...    2950.00     2973.00            3000.00
40   上海原油2306  150000     525.90  ...     528.00      531.70             540.70
41      铝2306  150000   18415.00  ...   18440.00    18470.00           18570.00
42   天然橡胶2309  150000   11750.00  ...   11765.00    11770.00           11850.00
43     沪锌2306  150000   21075.00  ...   21200.00    21280.00           21230.00
44      铜2306  150000   66400.00  ...   66900.00    67410.00           66770.00
45     黄金2306  150000     444.18  ...     444.06      444.04             446.94
46    螺纹钢2310  150000    3691.00  ...    3683.00     3660.00            3728.00
47     线材2305  150000    4380.00  ...    4524.00     4476.00            4487.00
48      铅2306  150000   15280.00  ...   15255.00    15255.00           15285.00
49     白银2306  150000    5611.00  ...    5587.00     5619.00            5629.00
50     沥青2307  150000    3654.00  ...    3679.00     3699.00            3696.00
51   热轧卷板2310  150000    3730.00  ...    3738.00     3721.00            3781.00
52      锡2306  150000  207010.00  ...  209320.00   212380.00          208140.00
53      镍2306  150000  179960.00  ...  182090.00   182320.00          179980.00
54     纸浆2309  150000    5096.00  ...    5076.00     5058.00            5126.00
55   20号胶2307  150000    9535.00  ...    9600.00     9620.00            9655.00
56    不锈钢2306  150000   15160.00  ...   15225.00    15300.00           15155.00
57  低硫燃料油2308  150000    3777.00  ...    3821.00     3840.00            3825.00
58    国际铜2307  150000   58760.00  ...   59150.00    59660.00           59100.00
59    工业硅2308  150015   15170.00  ...   15165.00    15190.00           15290.00
```

接口示例-订阅所有金融期货(中金所主力合约)

```
importtime
importakshareasak

cffex_text = ak.match_main_contract(symbol="cffex")

while True:
    time.sleep(3)
    futures_zh_spot_df = ak.futures_zh_spot(symbol=cffex_text, market="FF", adjust='0')
    print(futures_zh_spot_df)
```

数据示例-金融期货

```
        symbol      time      open      high       low current_price  \
0  沪深300指数1912  15:00:00  3902.800  3915.000  3871.200      3879.000
1    5年期国债1912  15:15:00    99.680    99.715    99.590        99.700
2   上证50指数1912  15:00:00  2972.800  2977.600  2948.800      2957.200
3  中证500指数1912  15:00:00  4841.200  4885.000  4800.800      4820.400
         hold volume         amount
0   93225.000  71712  279062965.600
1   18853.000   9786     975248.030
2   41928.000  25164   74617285.400
3  113365.000  73086  353463074.600
```
