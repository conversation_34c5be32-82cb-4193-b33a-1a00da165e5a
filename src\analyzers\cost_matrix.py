"""
成本矩阵生成模块
生成包含各品种、各月份合约的成本矩阵，便于监控和分析
"""
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import pandas as pd
import numpy as np
from dataclasses import asdict

from ..models.data_models import (
    BasisData, SpreadData, CostMatrix, FuturesContract
)


class CostMatrixGenerator:
    """成本矩阵生成器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def generate_cost_matrix(self, basis_costs: Dict[str, BasisData],
                           spread_costs: Dict[str, SpreadData],
                           contracts: List[FuturesContract]) -> CostMatrix:
        """生成成本矩阵"""
        return CostMatrix(
            timestamp=datetime.now(),
            basis_costs=basis_costs,
            spread_costs=spread_costs
        )
    
    def create_basis_matrix_dataframe(self, basis_costs: Dict[str, BasisData],
                                    contracts: List[FuturesContract]) -> pd.DataFrame:
        """创建基差成本矩阵DataFrame"""
        data = []
        
        # 按品种分组
        product_groups = self._group_contracts_by_product(contracts)
        
        for product_code, product_contracts in product_groups.items():
            # 按到期月份排序
            sorted_contracts = sorted(
                product_contracts,
                key=lambda x: x.contract_month
            )
            
            for contract in sorted_contracts:
                if contract.contract_code in basis_costs:
                    basis_data = basis_costs[contract.contract_code]
                    
                    data.append({
                        '品种': product_code,
                        '合约代码': contract.contract_code,
                        '合约名称': contract.contract_name,
                        '合约月份': contract.contract_month,
                        '是否主力': '是' if contract.is_main_contract else '否',
                        '期货价格': basis_data.futures_price,
                        '现货价格': basis_data.index_price,
                        '基差': basis_data.basis,
                        '基差率(%)': round(basis_data.basis_rate, 2),
                        '剩余天数': basis_data.remaining_days,
                        '年化成本(%)': round(basis_data.annualized_cost, 2),
                        '成交量': contract.volume,
                        '占比(%)': round(contract.volume_ratio, 2),
                        '到期日期': contract.expiry_date.strftime('%m-%d') if contract.expiry_date else '未知',
                        '更新时间': basis_data.timestamp.strftime('%H:%M:%S')
                    })
        
        df = pd.DataFrame(data)
        
        if not df.empty:
            # 按品种和月份排序
            df = df.sort_values(['品种', '合约月份'])
        
        return df
    
    def create_spread_matrix_dataframe(self, spread_costs: Dict[str, SpreadData],
                                     contracts: List[FuturesContract]) -> pd.DataFrame:
        """创建价差成本矩阵DataFrame"""
        data = []
        
        for spread_key, spread_data in spread_costs.items():
            # 提取品种信息
            near_product = spread_data.near_contract[:2]
            far_product = spread_data.far_contract[:2]
            
            # 只处理同品种的价差
            if near_product == far_product:
                # 提取月份信息
                near_month = spread_data.near_contract[-4:] if len(spread_data.near_contract) >= 6 else ''
                far_month = spread_data.far_contract[-4:] if len(spread_data.far_contract) >= 6 else ''
                
                data.append({
                    '品种': near_product,
                    '近月合约': spread_data.near_contract,
                    '远月合约': spread_data.far_contract,
                    '近月月份': near_month,
                    '远月月份': far_month,
                    '近月价格': spread_data.near_price,
                    '远月价格': spread_data.far_price,
                    '价差': round(spread_data.spread, 2),
                    '价差率(%)': round(spread_data.spread_rate, 2),
                    '时间差(天)': spread_data.time_diff_days,
                    '年化时间成本(%)': round(spread_data.annualized_time_cost, 2),
                    '更新时间': spread_data.timestamp.strftime('%H:%M:%S')
                })
        
        df = pd.DataFrame(data)
        
        if not df.empty:
            # 按品种和月份排序
            df = df.sort_values(['品种', '近月月份', '远月月份'])
        
        return df
    
    def create_comprehensive_matrix(self, basis_costs: Dict[str, BasisData],
                                  spread_costs: Dict[str, SpreadData],
                                  contracts: List[FuturesContract]) -> pd.DataFrame:
        """创建综合成本矩阵"""
        # 按品种分组
        product_groups = self._group_contracts_by_product(contracts)
        
        all_data = []
        
        for product_code, product_contracts in product_groups.items():
            # 按月份排序
            sorted_contracts = sorted(
                product_contracts,
                key=lambda x: x.contract_month
            )
            
            # 基差数据
            for contract in sorted_contracts:
                if contract.contract_code in basis_costs:
                    basis_data = basis_costs[contract.contract_code]
                    
                    all_data.append({
                        '类型': '基差成本',
                        '品种': product_code,
                        '主合约': contract.contract_code,
                        '对比合约': '现货指数',
                        '主合约月份': contract.contract_month,
                        '对比月份': '现货',
                        '主合约价格': basis_data.futures_price,
                        '对比价格': basis_data.index_price,
                        '价差': basis_data.basis,
                        '价差率(%)': round(basis_data.basis_rate, 2),
                        '时间因子(天)': basis_data.remaining_days,
                        '年化成本(%)': round(basis_data.annualized_cost, 2),
                        '风险等级': self._assess_risk_level(basis_data.annualized_cost),
                        '更新时间': basis_data.timestamp.strftime('%H:%M:%S')
                    })
            
            # 价差数据
            for spread_key, spread_data in spread_costs.items():
                if (spread_data.near_contract[:2] == product_code and
                    spread_data.far_contract[:2] == product_code):
                    
                    near_month = spread_data.near_contract[-4:] if len(spread_data.near_contract) >= 6 else ''
                    far_month = spread_data.far_contract[-4:] if len(spread_data.far_contract) >= 6 else ''
                    
                    all_data.append({
                        '类型': '价差成本',
                        '品种': product_code,
                        '主合约': spread_data.near_contract,
                        '对比合约': spread_data.far_contract,
                        '主合约月份': near_month,
                        '对比月份': far_month,
                        '主合约价格': spread_data.near_price,
                        '对比价格': spread_data.far_price,
                        '价差': spread_data.spread,
                        '价差率(%)': round(spread_data.spread_rate, 2),
                        '时间因子(天)': spread_data.time_diff_days,
                        '年化成本(%)': round(spread_data.annualized_time_cost, 2),
                        '风险等级': self._assess_risk_level(spread_data.annualized_time_cost),
                        '更新时间': spread_data.timestamp.strftime('%H:%M:%S')
                    })
        
        df = pd.DataFrame(all_data)
        
        if not df.empty:
            # 按品种、类型、月份排序
            df = df.sort_values(['品种', '类型', '主合约月份'])
        
        return df
    
    def create_heatmap_matrix(self, basis_costs: Dict[str, BasisData],
                            spread_costs: Dict[str, SpreadData]) -> pd.DataFrame:
        """创建热力图矩阵（用于可视化）"""
        # 收集所有品种和月份
        products = set()
        months = set()
        
        # 从基差数据收集
        for contract_code in basis_costs.keys():
            product = contract_code[:2]
            month = contract_code[-4:] if len(contract_code) >= 6 else ''
            products.add(product)
            months.add(month)
        
        # 从价差数据收集
        for spread_data in spread_costs.values():
            near_product = spread_data.near_contract[:2]
            far_product = spread_data.far_contract[:2]
            near_month = spread_data.near_contract[-4:] if len(spread_data.near_contract) >= 6 else ''
            far_month = spread_data.far_contract[-4:] if len(spread_data.far_contract) >= 6 else ''
            
            products.update([near_product, far_product])
            months.update([near_month, far_month])
        
        # 创建矩阵
        products = sorted(list(products))
        months = sorted(list(months))
        
        # 创建多级索引
        index = pd.MultiIndex.from_product([products, months], names=['品种', '月份'])
        columns = ['基差率(%)', '年化基差成本(%)', '价差率(%)', '年化时间成本(%)', '综合评分']
        
        matrix = pd.DataFrame(index=index, columns=columns)
        matrix = matrix.fillna(0)
        
        # 填充基差数据
        for contract_code, basis_data in basis_costs.items():
            product = contract_code[:2]
            month = contract_code[-4:] if len(contract_code) >= 6 else ''
            
            if product in products and month in months:
                matrix.loc[(product, month), '基差率(%)'] = basis_data.basis_rate
                matrix.loc[(product, month), '年化基差成本(%)'] = basis_data.annualized_cost
        
        # 填充价差数据（使用近月合约位置）
        for spread_data in spread_costs.values():
            near_product = spread_data.near_contract[:2]
            near_month = spread_data.near_contract[-4:] if len(spread_data.near_contract) >= 6 else ''
            
            if near_product in products and near_month in months:
                matrix.loc[(near_product, near_month), '价差率(%)'] = spread_data.spread_rate
                matrix.loc[(near_product, near_month), '年化时间成本(%)'] = spread_data.annualized_time_cost
        
        # 计算综合评分
        matrix['综合评分'] = (
            abs(matrix['基差率(%)']) * 0.3 +
            abs(matrix['年化基差成本(%)']) * 0.3 +
            abs(matrix['价差率(%)']) * 0.2 +
            abs(matrix['年化时间成本(%)']) * 0.2
        )
        
        return matrix

    def create_basis_spread_matrix_dataframe(self, basis_spread_costs: Dict[str, Dict],
                                           contracts: List[FuturesContract]) -> pd.DataFrame:
        """创建基差价差成本矩阵DataFrame"""
        data = []

        for contract1_code, spread_costs in basis_spread_costs.items():
            # 查找主合约信息
            contract1 = next((c for c in contracts if c.contract_code == contract1_code), None)
            if not contract1:
                continue

            for contract2_code, cost_data in spread_costs.items():
                # 查找对比合约信息
                contract2 = next((c for c in contracts if c.contract_code == contract2_code), None)
                if not contract2:
                    continue

                data.append({
                    '品种': contract1.product_code,
                    '主合约': contract1_code,
                    '对比合约': contract2_code,
                    '主合约月份': cost_data['contract1_month'],
                    '对比月份': cost_data['contract2_month'],
                    '主合约基差': round(cost_data['contract1_basis'], 2),
                    '对比基差': round(cost_data['contract2_basis'], 2),
                    '基差差值': round(cost_data['basis_diff'], 2),
                    '主合约剩余天数': cost_data['contract1_remaining_days'],
                    '对比剩余天数': cost_data['contract2_remaining_days'],
                    '天数差值': cost_data['days_diff'],
                    '年化基差价差成本(%)': round(cost_data['annualized_basis_spread_cost'], 2)
                })

        df = pd.DataFrame(data)

        if not df.empty:
            # 按品种、主合约月份排序
            df = df.sort_values(['品种', '主合约月份', '对比月份'])

        return df

    def create_comprehensive_cost_matrix(self, basis_costs: Dict[str, BasisData],
                                       basis_spread_costs: Dict[str, Dict],
                                       contracts: List[FuturesContract]) -> pd.DataFrame:
        """创建综合成本矩阵，合并基差成本和基差价差成本"""
        data = []

        # 按品种分组
        product_groups = self._group_contracts_by_product(contracts)

        for product_code, product_contracts in product_groups.items():
            # 按到期月份排序
            sorted_contracts = sorted(
                product_contracts,
                key=lambda x: x.contract_month
            )

            for contract in sorted_contracts:
                if contract.contract_code in basis_costs:
                    basis_data = basis_costs[contract.contract_code]

                    # 基差成本行
                    row = {
                        '类型': '基差成本',
                        '品种': product_code,
                        '主合约': contract.contract_code,
                        '对比合约': '现货指数',
                        '主合约月份': contract.contract_month,
                        '对比月份': '现货',
                        '主合约价格': round(basis_data.futures_price, 2),
                        '对比价格': round(basis_data.index_price, 2),
                        '价差': round(basis_data.basis, 2),
                        '价差率(%)': round(basis_data.basis_rate, 2),
                        '时间因子(天)': basis_data.remaining_days,
                        '年化成本(%)': round(basis_data.annualized_cost, 2),
                        '成交量': contract.volume,
                        '成交量占比(%)': round(contract.volume_ratio, 2),
                        '风险等级': self._assess_risk_level(basis_data.annualized_cost),
                        '更新时间': basis_data.timestamp.strftime('%H:%M:%S')
                    }
                    data.append(row)

                    # 基差价差成本行
                    if contract.contract_code in basis_spread_costs:
                        spread_costs = basis_spread_costs[contract.contract_code]

                        for contract2_code, cost_data in spread_costs.items():
                            contract2 = next((c for c in contracts if c.contract_code == contract2_code), None)
                            if contract2:
                                row = {
                                    '类型': '基差价差成本',
                                    '品种': product_code,
                                    '主合约': contract.contract_code,
                                    '对比合约': contract2_code,
                                    '主合约月份': contract.contract_month,
                                    '对比月份': contract2.contract_month,
                                    '主合约价格': round(basis_data.futures_price, 2),
                                    '对比价格': round(basis_costs[contract2_code].futures_price, 2) if contract2_code in basis_costs else 0,
                                    '价差': round(cost_data['basis_diff'], 2),
                                    '价差率(%)': round((cost_data['basis_diff'] / basis_data.futures_price * 100), 2) if basis_data.futures_price != 0 else 0,
                                    '时间因子(天)': cost_data['days_diff'],
                                    '年化成本(%)': round(cost_data['annualized_basis_spread_cost'], 2),
                                    '成交量': contract.volume,
                                    '成交量占比(%)': round(contract.volume_ratio, 2),
                                    '风险等级': self._assess_risk_level(cost_data['annualized_basis_spread_cost']),
                                    '更新时间': basis_data.timestamp.strftime('%H:%M:%S')
                                }
                                data.append(row)

        df = pd.DataFrame(data)

        if not df.empty:
            # 按品种、主合约月份、类型排序
            df = df.sort_values(['品种', '主合约月份', '类型', '对比月份'])

        return df

    def create_product_spread_matrices(self, basis_spread_costs: Dict[str, Dict],
                                     contracts: List[FuturesContract]) -> Dict[str, pd.DataFrame]:
        """创建按品种分组的4x4价差成本矩阵"""
        product_matrices = {}

        # 按品种分组合约
        product_groups = self._group_contracts_by_product(contracts)

        for product_code, product_contracts in product_groups.items():
            # 按月份排序合约
            sorted_contracts = sorted(
                product_contracts,
                key=lambda x: x.contract_month
            )

            # 获取合约名称列表
            contract_names = [c.contract_name for c in sorted_contracts]

            # 创建4x4矩阵
            matrix = pd.DataFrame(index=contract_names, columns=contract_names, dtype=float)
            matrix = matrix.fillna(0.0)

            # 填充矩阵数据
            for contract1 in sorted_contracts:
                if contract1.contract_code in basis_spread_costs:
                    spread_costs = basis_spread_costs[contract1.contract_code]

                    for contract2_code, cost_data in spread_costs.items():
                        # 找到对应的合约名称
                        contract2 = next((c for c in sorted_contracts if c.contract_code == contract2_code), None)
                        if contract2:
                            # 填充矩阵
                            cost_value = cost_data['annualized_basis_spread_cost']
                            # 如果天数差为0，显示为"-"
                            if cost_data['days_diff'] == 0:
                                matrix.loc[contract1.contract_name, contract2.contract_name] = "-"
                            else:
                                matrix.loc[contract1.contract_name, contract2.contract_name] = round(cost_value, 2)

            product_matrices[product_code] = matrix

        return product_matrices

    def create_integrated_product_matrices(self, basis_costs: Dict[str, BasisData],
                                         basis_spread_costs: Dict[str, Dict],
                                         contracts: List[FuturesContract]) -> Dict[str, pd.DataFrame]:
        """创建集成的品种成本矩阵，合并基差成本和价差成本"""
        integrated_matrices = {}

        # 按品种分组合约
        product_groups = self._group_contracts_by_product(contracts)

        for product_code, product_contracts in product_groups.items():
            # 按月份排序合约
            sorted_contracts = sorted(
                product_contracts,
                key=lambda x: x.contract_month
            )

            # 获取合约名称列表
            contract_names = [c.contract_name for c in sorted_contracts]

            # 创建列名：基差成本 + 价差成本列
            columns = ['基差成本(%)', '成交量', '成交量占比(%)'] + contract_names

            # 创建DataFrame
            matrix = pd.DataFrame(index=contract_names, columns=columns)

            # 填充基差成本数据
            for contract in sorted_contracts:
                if contract.contract_code in basis_costs:
                    basis_data = basis_costs[contract.contract_code]

                    matrix.loc[contract.contract_name, '基差成本(%)'] = round(basis_data.annualized_cost, 2)
                    matrix.loc[contract.contract_name, '成交量'] = contract.volume
                    matrix.loc[contract.contract_name, '成交量占比(%)'] = round(contract.volume_ratio, 2)

            # 填充价差成本数据
            for contract1 in sorted_contracts:
                if contract1.contract_code in basis_spread_costs:
                    spread_costs = basis_spread_costs[contract1.contract_code]

                    for contract2_code, cost_data in spread_costs.items():
                        # 找到对应的合约
                        contract2 = next((c for c in sorted_contracts if c.contract_code == contract2_code), None)
                        if contract2:
                            # 填充价差成本矩阵部分
                            cost_value = cost_data['annualized_basis_spread_cost']
                            # 如果天数差为0，显示为"-"
                            if cost_data['days_diff'] == 0:
                                matrix.loc[contract1.contract_name, contract2.contract_name] = "-"
                            else:
                                matrix.loc[contract1.contract_name, contract2.contract_name] = round(cost_value, 2)

                    # 对角线填充0（自己与自己的价差成本为0）
                    matrix.loc[contract1.contract_name, contract1.contract_name] = 0.0

            # 填充缺失值
            matrix = matrix.fillna(0)

            integrated_matrices[product_code] = matrix

        return integrated_matrices
    
    def generate_summary_statistics(self, cost_matrix: CostMatrix) -> Dict:
        """生成成本矩阵汇总统计"""
        summary = {
            'timestamp': cost_matrix.timestamp,
            'basis_summary': {},
            'spread_summary': {},
            'overall_summary': {}
        }
        
        # 基差汇总
        if cost_matrix.basis_costs:
            basis_rates = [data.basis_rate for data in cost_matrix.basis_costs.values()]
            annualized_costs = [data.annualized_cost for data in cost_matrix.basis_costs.values()]
            
            summary['basis_summary'] = {
                'count': len(cost_matrix.basis_costs),
                'avg_basis_rate': np.mean(basis_rates),
                'max_basis_rate': np.max(basis_rates),
                'min_basis_rate': np.min(basis_rates),
                'avg_annualized_cost': np.mean(annualized_costs),
                'max_annualized_cost': np.max(annualized_costs),
                'min_annualized_cost': np.min(annualized_costs)
            }
        
        # 价差汇总
        if cost_matrix.spread_costs:
            spread_rates = [data.spread_rate for data in cost_matrix.spread_costs.values()]
            time_costs = [data.annualized_time_cost for data in cost_matrix.spread_costs.values()]
            
            summary['spread_summary'] = {
                'count': len(cost_matrix.spread_costs),
                'avg_spread_rate': np.mean(spread_rates),
                'max_spread_rate': np.max(spread_rates),
                'min_spread_rate': np.min(spread_rates),
                'avg_time_cost': np.mean(time_costs),
                'max_time_cost': np.max(time_costs),
                'min_time_cost': np.min(time_costs)
            }
        
        # 整体汇总
        total_opportunities = len(cost_matrix.basis_costs) + len(cost_matrix.spread_costs)
        summary['overall_summary'] = {
            'total_opportunities': total_opportunities,
            'basis_opportunities': len(cost_matrix.basis_costs),
            'spread_opportunities': len(cost_matrix.spread_costs)
        }
        
        return summary
    
    def _group_contracts_by_product(self, contracts: List[FuturesContract]) -> Dict[str, List[FuturesContract]]:
        """按品种分组合约"""
        product_groups = {}
        for contract in contracts:
            if contract.product_code not in product_groups:
                product_groups[contract.product_code] = []
            product_groups[contract.product_code].append(contract)
        return product_groups
    
    def _assess_risk_level(self, annualized_cost: float) -> str:
        """评估风险等级"""
        abs_cost = abs(annualized_cost)
        
        if abs_cost < 1:
            return '低'
        elif abs_cost < 3:
            return '中'
        elif abs_cost < 5:
            return '高'
        else:
            return '极高'
    
    def export_to_excel(self, cost_matrix: CostMatrix, contracts: List[FuturesContract],
                       filename: str = None) -> str:
        """导出成本矩阵到Excel文件"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'cost_matrix_{timestamp}.xlsx'
        
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 基差矩阵
                basis_df = self.create_basis_matrix_dataframe(cost_matrix.basis_costs, contracts)
                basis_df.to_excel(writer, sheet_name='基差成本矩阵', index=False)
                
                # 价差矩阵
                spread_df = self.create_spread_matrix_dataframe(cost_matrix.spread_costs, contracts)
                spread_df.to_excel(writer, sheet_name='价差成本矩阵', index=False)
                
                # 综合矩阵
                comprehensive_df = self.create_comprehensive_matrix(
                    cost_matrix.basis_costs, cost_matrix.spread_costs, contracts
                )
                comprehensive_df.to_excel(writer, sheet_name='综合成本矩阵', index=False)
                
                # 汇总统计
                summary = self.generate_summary_statistics(cost_matrix)
                summary_df = pd.DataFrame([summary])
                summary_df.to_excel(writer, sheet_name='汇总统计', index=False)
            
            self.logger.info(f"成本矩阵已导出到: {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"导出Excel失败: {e}")
            raise
