"""
基差和年化成本计算模块
实现期货与现货价差计算、基差率计算、年化基差成本计算的核心算法
"""
import logging
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple
import numpy as np
import pandas as pd

from ..models.data_models import (
    FuturesQuote, IndexQuote, BasisData, FuturesContract
)
from ..utils.trading_calendar import TradingCalendar


class BasisCalculator:
    """基差计算器"""
    
    def __init__(self, trading_calendar: TradingCalendar, trading_days_per_year: int = 243):
        self.trading_calendar = trading_calendar
        self.trading_days_per_year = trading_days_per_year
        self.logger = logging.getLogger(__name__)
    
    async def calculate_basis(self, futures_quote: FuturesQuote, index_quote: IndexQuote,
                            contract: FuturesContract) -> BasisData:
        """计算基差数据"""
        try:
            # 基本价差计算
            basis = futures_quote.current_price - index_quote.current_price
            
            # 基差率计算（百分比）
            basis_rate = 0
            if index_quote.current_price != 0:
                basis_rate = (basis / index_quote.current_price) * 100
            
            # 计算剩余交易日
            remaining_days = 0
            if contract.expiry_date:
                remaining_days = await self.trading_calendar.get_remaining_trading_days(
                    contract.expiry_date
                )
            
            # 年化基差成本计算
            annualized_cost = await self._calculate_annualized_cost(
                basis_rate, remaining_days
            )
            
            return BasisData(
                futures_code=futures_quote.contract_code,
                index_code=index_quote.index_code,
                timestamp=datetime.now(),
                futures_price=futures_quote.current_price,
                index_price=index_quote.current_price,
                basis=basis,
                basis_rate=basis_rate,
                remaining_days=remaining_days,
                annualized_cost=annualized_cost
            )
            
        except Exception as e:
            self.logger.error(f"计算基差失败: {e}")
            raise
    
    async def calculate_multiple_basis(self, futures_quotes: Dict[str, FuturesQuote],
                                     index_quotes: Dict[str, IndexQuote],
                                     contracts: List[FuturesContract],
                                     futures_mapping: Dict[str, str]) -> Dict[str, BasisData]:
        """批量计算多个合约的基差"""
        results = {}
        
        for contract in contracts:
            try:
                # 获取期货行情
                if contract.contract_code not in futures_quotes:
                    self.logger.warning(f"缺少期货行情: {contract.contract_code}")
                    continue
                
                futures_quote = futures_quotes[contract.contract_code]
                
                # 获取对应的指数行情
                index_code = futures_mapping.get(contract.product_code)
                if not index_code or index_code not in index_quotes:
                    self.logger.warning(f"缺少指数行情: {index_code}")
                    continue
                
                index_quote = index_quotes[index_code]
                
                # 计算基差
                basis_data = await self.calculate_basis(futures_quote, index_quote, contract)
                results[contract.contract_code] = basis_data
                
            except Exception as e:
                self.logger.error(f"计算合约 {contract.contract_code} 基差失败: {e}")
                continue
        
        self.logger.info(f"成功计算 {len(results)} 个合约的基差")
        return results
    
    async def _calculate_annualized_cost(self, basis_rate: float, remaining_days: int) -> float:
        """计算年化基差成本"""
        if remaining_days <= 0:
            return 0
        
        # 年化成本 = (基差率 / 剩余天数) * 年交易天数
        annualized_cost = (basis_rate / remaining_days) * self.trading_days_per_year
        return annualized_cost
    
    def calculate_basis_statistics(self, basis_data_list: List[BasisData]) -> Dict:
        """计算基差统计信息"""
        if not basis_data_list:
            return {}
        
        basis_rates = [data.basis_rate for data in basis_data_list]
        annualized_costs = [data.annualized_cost for data in basis_data_list]
        remaining_days_list = [data.remaining_days for data in basis_data_list]
        
        return {
            'count': len(basis_data_list),
            'basis_rate_stats': {
                'mean': np.mean(basis_rates),
                'median': np.median(basis_rates),
                'std': np.std(basis_rates),
                'min': np.min(basis_rates),
                'max': np.max(basis_rates)
            },
            'annualized_cost_stats': {
                'mean': np.mean(annualized_costs),
                'median': np.median(annualized_costs),
                'std': np.std(annualized_costs),
                'min': np.min(annualized_costs),
                'max': np.max(annualized_costs)
            },
            'remaining_days_stats': {
                'mean': np.mean(remaining_days_list),
                'median': np.median(remaining_days_list),
                'min': np.min(remaining_days_list),
                'max': np.max(remaining_days_list)
            }
        }
    
    def analyze_basis_trend(self, historical_basis: List[BasisData]) -> Dict:
        """分析基差趋势"""
        if len(historical_basis) < 2:
            return {'trend': 'insufficient_data'}
        
        # 按时间排序
        sorted_data = sorted(historical_basis, key=lambda x: x.timestamp)
        
        # 计算趋势
        basis_rates = [data.basis_rate for data in sorted_data]
        time_points = list(range(len(basis_rates)))
        
        # 简单线性回归计算趋势
        if len(basis_rates) >= 2:
            slope = np.polyfit(time_points, basis_rates, 1)[0]
            
            trend = 'stable'
            if slope > 0.1:
                trend = 'increasing'
            elif slope < -0.1:
                trend = 'decreasing'
            
            return {
                'trend': trend,
                'slope': slope,
                'latest_basis_rate': basis_rates[-1],
                'change_from_first': basis_rates[-1] - basis_rates[0],
                'volatility': np.std(basis_rates)
            }
        
        return {'trend': 'insufficient_data'}
    
    def identify_arbitrage_opportunities(self, basis_data_list: List[BasisData],
                                       threshold_low: float = -0.5,
                                       threshold_high: float = 0.5) -> List[Dict]:
        """识别套利机会"""
        opportunities = []
        
        for data in basis_data_list:
            opportunity = None
            
            # 基差过低，可能的正向套利机会
            if data.basis_rate < threshold_low:
                opportunity = {
                    'type': 'positive_arbitrage',
                    'contract': data.futures_code,
                    'basis_rate': data.basis_rate,
                    'annualized_cost': data.annualized_cost,
                    'remaining_days': data.remaining_days,
                    'description': f'基差率 {data.basis_rate:.2f}% 过低，考虑买入期货卖出现货'
                }
            
            # 基差过高，可能的反向套利机会
            elif data.basis_rate > threshold_high:
                opportunity = {
                    'type': 'reverse_arbitrage',
                    'contract': data.futures_code,
                    'basis_rate': data.basis_rate,
                    'annualized_cost': data.annualized_cost,
                    'remaining_days': data.remaining_days,
                    'description': f'基差率 {data.basis_rate:.2f}% 过高，考虑卖出期货买入现货'
                }
            
            if opportunity:
                opportunities.append(opportunity)
        
        return opportunities
    
    def calculate_carry_cost(self, basis_data: BasisData, risk_free_rate: float = 0.03) -> Dict:
        """计算持有成本"""
        # 理论基差 = 现货价格 * (无风险利率 - 股息率) * 时间
        # 这里简化处理，假设股息率为0
        
        time_to_expiry = basis_data.remaining_days / self.trading_days_per_year
        theoretical_basis_rate = risk_free_rate * time_to_expiry * 100
        
        # 实际基差与理论基差的偏差
        basis_deviation = basis_data.basis_rate - theoretical_basis_rate
        
        return {
            'theoretical_basis_rate': theoretical_basis_rate,
            'actual_basis_rate': basis_data.basis_rate,
            'basis_deviation': basis_deviation,
            'time_to_expiry_years': time_to_expiry,
            'risk_free_rate': risk_free_rate,
            'interpretation': self._interpret_carry_cost(basis_deviation)
        }
    
    def _interpret_carry_cost(self, deviation: float) -> str:
        """解释持有成本偏差"""
        if abs(deviation) < 0.1:
            return "基差接近理论值，市场定价合理"
        elif deviation > 0.1:
            return "基差高于理论值，期货可能被高估"
        else:
            return "基差低于理论值，期货可能被低估"
    
    def create_basis_report(self, basis_data_list: List[BasisData]) -> Dict:
        """生成基差分析报告"""
        if not basis_data_list:
            return {'error': '无基差数据'}
        
        # 基础统计
        stats = self.calculate_basis_statistics(basis_data_list)
        
        # 套利机会
        opportunities = self.identify_arbitrage_opportunities(basis_data_list)
        
        # 按品种分组
        by_product = {}
        for data in basis_data_list:
            product = data.futures_code[:2]  # 提取品种代码
            if product not in by_product:
                by_product[product] = []
            by_product[product].append(data)
        
        # 生成报告
        report = {
            'timestamp': datetime.now(),
            'summary': {
                'total_contracts': len(basis_data_list),
                'products_count': len(by_product),
                'arbitrage_opportunities': len(opportunities)
            },
            'statistics': stats,
            'arbitrage_opportunities': opportunities,
            'by_product': {}
        }
        
        # 按品种统计
        for product, product_data in by_product.items():
            product_stats = self.calculate_basis_statistics(product_data)
            report['by_product'][product] = {
                'contracts_count': len(product_data),
                'statistics': product_stats,
                'contracts': [
                    {
                        'code': data.futures_code,
                        'basis_rate': data.basis_rate,
                        'annualized_cost': data.annualized_cost,
                        'remaining_days': data.remaining_days
                    }
                    for data in product_data
                ]
            }
        
        return report
