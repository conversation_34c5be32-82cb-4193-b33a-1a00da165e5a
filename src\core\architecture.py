"""
金融股指期货监控系统架构设计
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Protocol
from datetime import datetime
import asyncio
import logging

from ..models.data_models import (
    FuturesContract, FuturesQuote, IndexQuote, 
    BasisData, SpreadData, CostMatrix, MonitoringConfig
)


class DataProvider(Protocol):
    """数据提供者协议"""
    
    async def get_futures_quotes(self, contracts: List[str]) -> Dict[str, FuturesQuote]:
        """获取期货行情"""
        ...
    
    async def get_index_quotes(self, indices: List[str]) -> Dict[str, IndexQuote]:
        """获取指数行情"""
        ...
    
    async def get_futures_contracts(self, product_codes: List[str]) -> List[FuturesContract]:
        """获取期货合约信息"""
        ...
    
    async def get_trading_days(self, start_date: str, end_date: str) -> List[str]:
        """获取交易日历"""
        ...


class Calculator(ABC):
    """计算器抽象基类"""
    
    @abstractmethod
    def calculate(self, *args, **kwargs):
        """执行计算"""
        pass


class BasisCalculator(Calculator):
    """基差计算器"""
    
    def __init__(self, trading_days_per_year: int = 243):
        self.trading_days_per_year = trading_days_per_year
    
    def calculate(self, futures_quote: FuturesQuote, index_quote: IndexQuote, 
                 remaining_days: int) -> BasisData:
        """计算基差数据"""
        basis = futures_quote.current_price - index_quote.current_price
        basis_rate = (basis / index_quote.current_price) * 100 if index_quote.current_price != 0 else 0
        
        # 年化基差成本计算
        annualized_cost = 0
        if remaining_days > 0:
            annualized_cost = (basis_rate / remaining_days) * self.trading_days_per_year
        
        return BasisData(
            futures_code=futures_quote.contract_code,
            index_code=index_quote.index_code,
            timestamp=datetime.now(),
            futures_price=futures_quote.current_price,
            index_price=index_quote.current_price,
            basis=basis,
            basis_rate=basis_rate,
            remaining_days=remaining_days,
            annualized_cost=annualized_cost
        )


class SpreadCalculator(Calculator):
    """价差计算器"""
    
    def __init__(self, trading_days_per_year: int = 243):
        self.trading_days_per_year = trading_days_per_year
    
    def calculate(self, near_quote: FuturesQuote, far_quote: FuturesQuote,
                 time_diff_days: int) -> SpreadData:
        """计算价差数据"""
        spread = far_quote.current_price - near_quote.current_price
        spread_rate = (spread / near_quote.current_price) * 100 if near_quote.current_price != 0 else 0
        
        # 年化时间成本计算
        annualized_time_cost = 0
        if time_diff_days > 0:
            annualized_time_cost = (spread_rate / time_diff_days) * self.trading_days_per_year
        
        return SpreadData(
            near_contract=near_quote.contract_code,
            far_contract=far_quote.contract_code,
            timestamp=datetime.now(),
            near_price=near_quote.current_price,
            far_price=far_quote.current_price,
            spread=spread,
            spread_rate=spread_rate,
            time_diff_days=time_diff_days,
            annualized_time_cost=annualized_time_cost
        )


class DataStorage(ABC):
    """数据存储抽象基类"""
    
    @abstractmethod
    async def save_quotes(self, quotes: Dict[str, FuturesQuote]):
        """保存行情数据"""
        pass
    
    @abstractmethod
    async def save_cost_matrix(self, matrix: CostMatrix):
        """保存成本矩阵"""
        pass
    
    @abstractmethod
    async def get_historical_data(self, start_date: datetime, end_date: datetime):
        """获取历史数据"""
        pass


class EventBus:
    """事件总线"""
    
    def __init__(self):
        self._subscribers: Dict[str, List[callable]] = {}
    
    def subscribe(self, event_type: str, callback: callable):
        """订阅事件"""
        if event_type not in self._subscribers:
            self._subscribers[event_type] = []
        self._subscribers[event_type].append(callback)
    
    async def publish(self, event_type: str, data: any):
        """发布事件"""
        if event_type in self._subscribers:
            for callback in self._subscribers[event_type]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data)
                    else:
                        callback(data)
                except Exception as e:
                    logging.error(f"Event callback error: {e}")


class MonitoringEngine:
    """监控引擎核心类"""
    
    def __init__(self, config: MonitoringConfig, data_provider: DataProvider,
                 storage: Optional[DataStorage] = None):
        self.config = config
        self.data_provider = data_provider
        self.storage = storage
        self.event_bus = EventBus()
        
        # 计算器
        self.basis_calculator = BasisCalculator(config.trading_days_per_year)
        self.spread_calculator = SpreadCalculator(config.trading_days_per_year)
        
        # 状态
        self.is_running = False
        self.active_contracts: List[FuturesContract] = []
        
        # 日志
        self.logger = logging.getLogger(__name__)
    
    async def start(self):
        """启动监控引擎"""
        self.is_running = True
        self.logger.info("监控引擎启动")
        
        # 初始化合约信息
        await self._initialize_contracts()
        
        # 启动主循环
        await self._main_loop()
    
    async def stop(self):
        """停止监控引擎"""
        self.is_running = False
        self.logger.info("监控引擎停止")
    
    async def _initialize_contracts(self):
        """初始化合约信息"""
        product_codes = list(self.config.futures_mapping.keys())
        self.active_contracts = await self.data_provider.get_futures_contracts(product_codes)
        self.logger.info(f"初始化了 {len(self.active_contracts)} 个合约")
    
    async def _main_loop(self):
        """主监控循环"""
        while self.is_running:
            try:
                # 获取数据并计算成本矩阵
                cost_matrix = await self._calculate_cost_matrix()
                
                # 发布数据更新事件
                await self.event_bus.publish("cost_matrix_updated", cost_matrix)
                
                # 保存数据
                if self.storage:
                    await self.storage.save_cost_matrix(cost_matrix)
                
                # 等待下次更新
                await asyncio.sleep(self.config.update_interval)
                
            except Exception as e:
                self.logger.error(f"监控循环错误: {e}")
                await asyncio.sleep(10)  # 错误时短暂等待
    
    async def _calculate_cost_matrix(self) -> CostMatrix:
        """计算成本矩阵"""
        # 获取期货行情
        contract_codes = [c.contract_code for c in self.active_contracts]
        futures_quotes = await self.data_provider.get_futures_quotes(contract_codes)
        
        # 获取指数行情
        index_codes = list(self.config.futures_mapping.values())
        index_quotes = await self.data_provider.get_index_quotes(index_codes)
        
        # 计算基差成本
        basis_costs = {}
        for contract in self.active_contracts:
            if contract.contract_code in futures_quotes:
                futures_quote = futures_quotes[contract.contract_code]
                index_code = self.config.futures_mapping.get(contract.product_code)
                
                if index_code and index_code in index_quotes:
                    index_quote = index_quotes[index_code]
                    
                    # 计算剩余交易日
                    remaining_days = await self._get_remaining_trading_days(contract)
                    
                    # 计算基差
                    basis_data = self.basis_calculator.calculate(
                        futures_quote, index_quote, remaining_days
                    )
                    basis_costs[contract.contract_code] = basis_data
        
        # 计算价差成本
        spread_costs = await self._calculate_spread_costs(futures_quotes)
        
        return CostMatrix(
            timestamp=datetime.now(),
            basis_costs=basis_costs,
            spread_costs=spread_costs
        )
    
    async def _calculate_spread_costs(self, futures_quotes: Dict[str, FuturesQuote]) -> Dict[str, SpreadData]:
        """计算价差成本"""
        spread_costs = {}
        
        # 按品种分组合约
        product_contracts = {}
        for contract in self.active_contracts:
            if contract.product_code not in product_contracts:
                product_contracts[contract.product_code] = []
            product_contracts[contract.product_code].append(contract)
        
        # 计算同品种不同月份合约间的价差
        for product_code, contracts in product_contracts.items():
            contracts.sort(key=lambda x: x.contract_month)  # 按月份排序
            
            for i in range(len(contracts) - 1):
                near_contract = contracts[i]
                far_contract = contracts[i + 1]
                
                if (near_contract.contract_code in futures_quotes and 
                    far_contract.contract_code in futures_quotes):
                    
                    near_quote = futures_quotes[near_contract.contract_code]
                    far_quote = futures_quotes[far_contract.contract_code]
                    
                    # 计算时间差
                    time_diff_days = await self._get_time_diff_days(near_contract, far_contract)
                    
                    # 计算价差
                    spread_data = self.spread_calculator.calculate(
                        near_quote, far_quote, time_diff_days
                    )
                    
                    spread_key = f"{near_contract.contract_code}_{far_contract.contract_code}"
                    spread_costs[spread_key] = spread_data
        
        return spread_costs
    
    async def _get_remaining_trading_days(self, contract: FuturesContract) -> int:
        """获取合约剩余交易日"""
        if not contract.expiry_date:
            return 0
        
        today = datetime.now().strftime('%Y%m%d')
        expiry = contract.expiry_date.strftime('%Y%m%d')
        
        trading_days = await self.data_provider.get_trading_days(today, expiry)
        return len(trading_days)
    
    async def _get_time_diff_days(self, near_contract: FuturesContract, 
                                 far_contract: FuturesContract) -> int:
        """获取两个合约间的时间差（交易日）"""
        if not near_contract.expiry_date or not far_contract.expiry_date:
            return 0
        
        near_expiry = near_contract.expiry_date.strftime('%Y%m%d')
        far_expiry = far_contract.expiry_date.strftime('%Y%m%d')
        
        trading_days = await self.data_provider.get_trading_days(near_expiry, far_expiry)
        return len(trading_days)
