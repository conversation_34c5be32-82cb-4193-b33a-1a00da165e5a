#!/usr/bin/env python3
"""
AKShare数据提供者
提供期货基础信息和实时行情数据
"""

import asyncio
import logging
import time
import os
import pickle
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple
import pandas as pd
import akshare as ak
from concurrent.futures import ThreadPoolExecutor

from ..models.data_models import FuturesContract, FuturesQuote, IndexQuote
from ..core.architecture import DataProvider
from ..utils.retry_utils import retry_with_backoff, RetryConfig


class AKShareProvider(DataProvider):
    """AKShare数据提供者"""
    
    def __init__(self, max_workers: int = 4):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 重试配置
        self.retry_config = RetryConfig(
            max_retries=3,
            base_delay=1.0,
            max_delay=10.0,
            backoff_factor=2.0
        )
        
        # 缓存期货基础信息
        self._futures_base_info: Optional[pd.DataFrame] = None
        self._last_base_info_update: Optional[datetime] = None
        self._base_info_cache_duration = 86400  # 24小时缓存（一天）
        
        # 本地文件缓存
        self._cache_dir = "cache"
        self._base_info_cache_file = os.path.join(self._cache_dir, "akshare_futures_base_info.pkl")
        
        # 确保缓存目录存在
        if not os.path.exists(self._cache_dir):
            os.makedirs(self._cache_dir)
        
        # 品种代码映射
        self.product_mapping = {
            'IC': '中证500',
            'IF': '沪深300', 
            'IH': '上证50',
            'IM': '中证1000'
        }
    
    async def get_futures_contracts(self, product_codes: List[str] = None) -> List[FuturesContract]:
        """获取期货合约列表"""
        try:
            # 获取期货基础信息
            base_info = await self._get_futures_base_info()
            if base_info is None or base_info.empty:
                self.logger.error("无法获取期货基础信息")
                return []
            
            contracts = []
            
            # 按品种分组处理
            for product_code in (product_codes or ['IC', 'IF', 'IH', 'IM']):
                self.logger.info(f"处理 {product_code} 品种合约")
                
                # 过滤该品种的合约
                product_contracts = base_info[
                    base_info['合约代码'].str.startswith(product_code, na=False)
                ].copy()
                
                if product_contracts.empty:
                    self.logger.warning(f"未找到 {product_code} 品种的合约")
                    continue
                
                # 过滤掉连续合约，只保留具体月份合约
                continuous_patterns = ['当月连续', '下月连续', '下季连续', '隔季连续', '主力合约', '次主力合']
                specific_contracts = product_contracts[
                    ~product_contracts['合约名称'].str.contains('|'.join(continuous_patterns), na=False)
                ].copy()
                
                if specific_contracts.empty:
                    self.logger.warning(f"过滤连续合约后，未找到 {product_code} 品种的具体月份合约")
                    continue
                
                # 按成交量排序，限制每个品种最多4个合约
                specific_contracts = specific_contracts.sort_values('成交量', ascending=False).head(4)
                
                # 计算品种总成交量
                total_volume = specific_contracts['成交量'].sum()
                
                self.logger.info(f"找到 {len(specific_contracts)} 个 {product_code} 具体月份合约")
                
                # 创建合约对象
                for idx, row in specific_contracts.iterrows():
                    try:
                        contract_code = str(row['合约代码'])
                        contract_name = str(row['合约名称'])
                        current_price = float(row.get('最新价', 0))
                        volume = int(row.get('成交量', 0))
                        
                        # 解析到期日期和月份
                        expiry_date, contract_month = await self._parse_contract_info(contract_code, contract_name)
                        
                        # 计算成交量占比
                        volume_ratio = (volume / total_volume * 100) if total_volume > 0 else 0
                        
                        contract = FuturesContract(
                            contract_code=contract_code,
                            product_code=product_code,
                            contract_name=contract_name,
                            quote_id=f"akshare.{contract_code}",
                            market_type='中金所',
                            expiry_date=expiry_date,
                            is_main_contract=(idx == specific_contracts.index[0]),  # 成交量最大的为主力合约
                            volume=volume,
                            volume_ratio=volume_ratio
                        )
                        
                        contracts.append(contract)
                        self.logger.debug(f"创建合约: {contract_code} - {contract_name}")
                        
                    except Exception as e:
                        self.logger.warning(f"创建合约失败 {row.get('合约代码', 'Unknown')}: {e}")
                        continue
            
            self.logger.info(f"总共获取到 {len(contracts)} 个期货合约")
            return contracts
            
        except Exception as e:
            self.logger.error(f"获取期货合约失败: {e}")
            return []
    
    async def get_futures_quotes(self, contracts: List[FuturesContract]) -> Dict[str, FuturesQuote]:
        """获取期货行情数据"""
        try:
            # 获取实时行情数据
            quotes_data = await self._get_futures_realtime_quotes()
            if quotes_data is None or quotes_data.empty:
                self.logger.error("无法获取期货实时行情")
                return {}
            
            quotes = {}
            
            for contract in contracts:
                try:
                    # 查找对应的行情数据
                    contract_data = quotes_data[
                        quotes_data['代码'].str.contains(contract.contract_code, na=False)
                    ]
                    
                    if contract_data.empty:
                        self.logger.warning(f"未找到合约 {contract.contract_code} 的行情数据")
                        continue
                    
                    row = contract_data.iloc[0]
                    
                    quote = FuturesQuote(
                        contract_code=contract.contract_code,
                        timestamp=datetime.now(),
                        current_price=float(row.get('最新价', 0)),
                        open_price=float(row.get('今开', 0)),
                        high_price=float(row.get('最高', 0)),
                        low_price=float(row.get('最低', 0)),
                        volume=int(row.get('成交量', 0)),
                        amount=float(row.get('成交额', 0)),
                        prev_close=float(row.get('昨收', 0)),
                        change_pct=float(row.get('涨跌幅', 0)),
                        change_amount=float(row.get('涨跌额', 0)),
                        bid_price=0,  # akshare可能不提供买卖价
                        ask_price=0,
                        open_interest=int(row.get('持仓量', 0))
                    )
                    
                    quotes[contract.contract_code] = quote
                    self.logger.debug(f"获取行情: {contract.contract_code} - 价格: {quote.current_price}")
                    
                except Exception as e:
                    self.logger.warning(f"处理合约 {contract.contract_code} 行情失败: {e}")
                    continue
            
            self.logger.info(f"获取到 {len(quotes)} 个合约的行情数据")
            return quotes
            
        except Exception as e:
            self.logger.error(f"获取期货行情失败: {e}")
            return {}
    
    async def _get_futures_base_info(self) -> Optional[pd.DataFrame]:
        """获取期货基础信息（带本地文件缓存）"""
        now = datetime.now()
        
        # 检查内存缓存是否有效
        if (self._futures_base_info is not None and
            self._last_base_info_update is not None and
            (now - self._last_base_info_update).total_seconds() < self._base_info_cache_duration):
            self.logger.debug("使用内存缓存的期货基础信息")
            return self._futures_base_info
        
        # 尝试从本地文件加载缓存
        cached_data = await self._load_base_info_from_file()
        if cached_data is not None:
            self._futures_base_info = cached_data
            self._last_base_info_update = now
            self.logger.info("从本地文件加载期货基础信息缓存")
            return cached_data
        
        # 尝试从API获取新数据
        async def _fetch_base_info():
            self.logger.info("从AKShare API获取期货基础信息...")
            
            # 获取期货手续费与保证金信息
            base_info = await asyncio.get_event_loop().run_in_executor(
                self.executor, ak.futures_comm_info, "中国金融期货交易所"
            )
            
            # 保存到内存缓存
            self._futures_base_info = base_info
            self._last_base_info_update = now
            
            # 保存到本地文件
            await self._save_base_info_to_file(base_info)
            
            self.logger.info("更新期货基础信息缓存并保存到本地文件")
            return base_info
        
        try:
            # 使用重试机制获取数据
            return await retry_with_backoff(
                _fetch_base_info,
                self.retry_config,
                self.logger,
                "获取期货基础信息"
            )
            
        except Exception as e:
            self.logger.error(f"获取期货基础信息最终失败: {e}")
            
            # 如果API调用失败，尝试使用旧的本地缓存文件
            old_cached_data = await self._load_base_info_from_file(ignore_expiry=True)
            if old_cached_data is not None:
                self.logger.warning("API调用失败，使用旧的本地缓存文件")
                self._futures_base_info = old_cached_data
                return old_cached_data
            
            return None
    
    async def _get_futures_realtime_quotes(self) -> Optional[pd.DataFrame]:
        """获取期货实时行情数据"""
        try:
            async def _fetch_quotes():
                # 获取内盘期货实时行情
                quotes_data = await asyncio.get_event_loop().run_in_executor(
                    self.executor, ak.futures_zh_spot_em
                )
                return quotes_data
            
            return await retry_with_backoff(
                _fetch_quotes,
                self.retry_config,
                self.logger,
                "获取期货实时行情"
            )
            
        except Exception as e:
            self.logger.error(f"获取期货实时行情失败: {e}")
            return None

    async def _parse_contract_info(self, contract_code: str, contract_name: str) -> Tuple[date, str]:
        """解析合约信息，获取到期日期和月份"""
        try:
            # 从合约代码中提取年月信息
            # 例如: IC2501 -> 2025年01月
            if len(contract_code) >= 6:
                year_month = contract_code[-4:]  # 取后4位
                year = int("20" + year_month[:2])  # 前两位是年份
                month = int(year_month[2:])  # 后两位是月份

                # 计算到期日期（第三个周五）
                expiry_date = self._calculate_expiry_date(year, month)
                contract_month = f"{year:04d}{month:02d}"

                return expiry_date, contract_month
            else:
                # 如果无法解析，使用默认值
                today = date.today()
                return today, today.strftime("%Y%m")

        except Exception as e:
            self.logger.warning(f"解析合约信息失败 {contract_code}: {e}")
            today = date.today()
            return today, today.strftime("%Y%m")

    def _calculate_expiry_date(self, year: int, month: int) -> date:
        """计算期货合约到期日期（第三个周五）"""
        try:
            # 找到该月第一天
            first_day = date(year, month, 1)

            # 找到第一个周五
            days_until_friday = (4 - first_day.weekday()) % 7
            first_friday = first_day + timedelta(days=days_until_friday)

            # 第三个周五
            third_friday = first_friday + timedelta(days=14)

            # 如果第三个周五超出了当月，则使用当月最后一个周五
            if third_friday.month != month:
                # 找到当月最后一个周五
                last_day = date(year, month + 1, 1) - timedelta(days=1) if month < 12 else date(year, 12, 31)
                days_back_to_friday = (last_day.weekday() - 4) % 7
                third_friday = last_day - timedelta(days=days_back_to_friday)

            return third_friday

        except Exception as e:
            self.logger.warning(f"计算到期日期失败 {year}-{month}: {e}")
            return date(year, month, 15)  # 默认使用月中

    async def _save_base_info_to_file(self, data: pd.DataFrame) -> None:
        """保存期货基础信息到本地文件"""
        try:
            cache_data = {
                'data': data,
                'timestamp': datetime.now(),
                'version': '1.0'
            }

            with open(self._base_info_cache_file, 'wb') as f:
                pickle.dump(cache_data, f)

            self.logger.debug(f"期货基础信息已保存到: {self._base_info_cache_file}")

        except Exception as e:
            self.logger.warning(f"保存期货基础信息到本地文件失败: {e}")

    async def _load_base_info_from_file(self, ignore_expiry: bool = False) -> Optional[pd.DataFrame]:
        """从本地文件加载期货基础信息"""
        try:
            if not os.path.exists(self._base_info_cache_file):
                return None

            with open(self._base_info_cache_file, 'rb') as f:
                cache_data = pickle.load(f)

            # 检查缓存是否过期
            if not ignore_expiry:
                cache_time = cache_data.get('timestamp', datetime.min)
                now = datetime.now()

                if (now - cache_time).total_seconds() > self._base_info_cache_duration:
                    self.logger.debug("本地缓存文件已过期")
                    return None

            data = cache_data.get('data')
            if data is not None and not data.empty:
                self.logger.debug(f"从本地文件加载期货基础信息: {len(data)} 条记录")
                return data

        except Exception as e:
            self.logger.warning(f"从本地文件加载期货基础信息失败: {e}")

        return None

    async def get_index_quotes(self, index_codes: List[str]) -> Dict[str, IndexQuote]:
        """获取指数行情（AKShare不直接提供指数数据，返回空字典）"""
        self.logger.info("AKShare提供者不支持指数行情数据")
        return {}

    async def cleanup(self):
        """清理资源"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=True)
        self.logger.info("AKShare提供者资源已清理")
