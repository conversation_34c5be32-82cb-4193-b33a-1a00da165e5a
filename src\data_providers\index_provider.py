"""
现货指数数据提供者实现
支持efinance和adata双数据源
"""
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional
import pandas as pd
import efinance as ef
from concurrent.futures import ThreadPoolExecutor

from ..models.data_models import IndexQuote


class IndexDataProvider:
    """现货指数数据提供者"""
    
    def __init__(self, max_workers: int = 4, use_adata_fallback: bool = True):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.logger = logging.getLogger(__name__)
        self.use_adata_fallback = use_adata_fallback
        
        # 尝试导入adata
        self.adata_available = False
        if use_adata_fallback:
            try:
                import adata
                self.adata = adata
                self.adata_available = True
                self.logger.info("adata模块可用，将作为备用数据源")
            except ImportError:
                self.logger.warning("adata模块不可用，仅使用efinance数据源")
    
    async def get_index_quotes_efinance(self, indices: List[str]) -> Dict[str, IndexQuote]:
        """使用efinance获取指数行情"""
        result = {}

        try:
            # 使用新的方法获取所有沪深系列指数
            quotes_df = await asyncio.get_event_loop().run_in_executor(
                self.executor, ef.stock.get_realtime_quotes, '沪深系列指数'
            )

            # 创建指数名称到代码的映射
            index_name_mapping = {
                '沪深300': 'sh000300',
                '中证500': 'sh000905',
                '中证1000': 'sh000852',
                '上证50': 'sh000016'
            }

            for _, row in quotes_df.iterrows():
                # 使用股票名称字段进行筛选
                stock_name = row.get('股票名称', '')

                # 检查是否是我们需要的指数
                if stock_name in index_name_mapping:
                    index_code = index_name_mapping[stock_name]

                    # 只处理请求的指数
                    if index_code in indices:
                        quote = IndexQuote(
                            index_code=index_code,
                            index_name=stock_name,
                            timestamp=datetime.now(),
                            current_price=float(row.get('最新价', 0)),
                            open_price=float(row.get('今开', 0)),
                            high_price=float(row.get('最高', 0)),
                            low_price=float(row.get('最低', 0)),
                            volume=int(row.get('成交量', 0)),
                            amount=float(row.get('成交额', 0)),
                            prev_close=float(row.get('昨日收盘', 0)),
                            change_pct=float(row.get('涨跌幅', 0)),
                            change_amount=float(row.get('涨跌额', 0))
                        )
                        result[index_code] = quote
                        self.logger.debug(f"找到指数: {stock_name} ({index_code}) - 价格: {row.get('最新价', 0)}")

        except Exception as e:
            self.logger.warning(f"获取指数行情失败: {e}")

        self.logger.info(f"efinance获取到 {len(result)} 个指数行情")
        return result
    
    async def get_index_quotes_adata(self, indices: List[str]) -> Dict[str, IndexQuote]:
        """使用adata获取指数行情"""
        if not self.adata_available:
            return {}

        result = {}

        # 指数代码映射
        index_mapping = {
            'sh000300': ('000300', '沪深300'),
            'sh000905': ('000905', '中证500'),
            'sh000852': ('000852', '中证1000'),
            'sh000016': ('000016', '上证50'),
            'sh000001': ('000001', '上证指数'),
            'sz399001': ('399001', '深证成指'),
            'sz399006': ('399006', '创业板指')
        }

        for index_code in indices:
            try:
                if index_code not in index_mapping:
                    self.logger.warning(f"不支持的指数代码: {index_code}")
                    continue

                adata_code, index_name = index_mapping[index_code]

                # 获取指数实时行情
                quote_df = await asyncio.get_event_loop().run_in_executor(
                    self.executor,
                    self.adata.stock.market.get_market_index_current,
                    adata_code
                )

                if not quote_df.empty:
                    row = quote_df.iloc[0]

                    # 获取当前价格
                    current_price = float(row.get('close', row.get('price', 0)))
                    open_price = float(row.get('open', 0))
                    high_price = float(row.get('high', 0))
                    low_price = float(row.get('low', 0))
                    volume = int(row.get('volume', 0))
                    amount = float(row.get('amount', 0))

                    # 尝试获取昨收价
                    prev_close = float(row.get('pre_close', 0))
                    if prev_close == 0 and open_price > 0:
                        # 如果没有昨收价，使用开盘价作为估算
                        prev_close = open_price

                    # 计算涨跌幅和涨跌额
                    change_amount = 0
                    change_pct = 0
                    if prev_close > 0:
                        change_amount = current_price - prev_close
                        change_pct = (change_amount / prev_close) * 100

                    quote = IndexQuote(
                        index_code=index_code,
                        index_name=index_name,
                        timestamp=datetime.now(),
                        current_price=current_price,
                        open_price=open_price,
                        high_price=high_price,
                        low_price=low_price,
                        volume=volume,
                        amount=amount,
                        prev_close=prev_close,
                        change_pct=change_pct,
                        change_amount=change_amount
                    )

                    result[index_code] = quote
                    self.logger.debug(f"adata获取指数: {index_name} ({index_code}) - 价格: {current_price}")

            except Exception as e:
                self.logger.warning(f"adata获取指数 {index_code} 行情失败: {e}")
                continue

        self.logger.info(f"adata获取到 {len(result)} 个指数行情")
        return result
    
    async def get_index_quotes(self, indices: List[str]) -> Dict[str, IndexQuote]:
        """获取指数行情（主方法，优先使用adata）"""
        result = {}

        # 优先使用adata
        if self.adata_available:
            self.logger.info("优先使用adata获取指数行情")
            result = await self.get_index_quotes_adata(indices)

        # 如果adata获取失败或数据不完整，尝试使用efinance补充
        if len(result) < len(indices):
            missing_indices = [idx for idx in indices if idx not in result]
            if missing_indices:
                self.logger.info(f"使用efinance补充缺失的指数数据: {missing_indices}")
                efinance_result = await self.get_index_quotes_efinance(missing_indices)
                result.update(efinance_result)

        return result
    
    async def get_all_index_codes(self) -> List[Dict[str, str]]:
        """获取所有可用的指数代码"""
        result = []
        
        # 从adata获取指数代码列表
        if self.adata_available:
            try:
                codes_df = await asyncio.get_event_loop().run_in_executor(
                    self.executor, self.adata.stock.info.all_index_code
                )
                
                for _, row in codes_df.iterrows():
                    result.append({
                        'index_code': row.get('index_code', ''),
                        'name': row.get('name', ''),
                        'source': 'adata'
                    })
                    
            except Exception as e:
                self.logger.error(f"获取adata指数代码列表失败: {e}")
        
        # 添加常用的指数代码
        common_indices = [
            {'index_code': 'sh000300', 'name': '沪深300', 'source': 'common'},
            {'index_code': 'sh000905', 'name': '中证500', 'source': 'common'},
            {'index_code': 'sh000852', 'name': '中证1000', 'source': 'common'},
            {'index_code': 'sh000016', 'name': '上证50', 'source': 'common'},
            {'index_code': 'sh000001', 'name': '上证指数', 'source': 'common'},
            {'index_code': 'sz399001', 'name': '深证成指', 'source': 'common'},
            {'index_code': 'sz399006', 'name': '创业板指', 'source': 'common'},
        ]
        
        result.extend(common_indices)
        return result
    
    async def get_trading_calendar_adata(self, year: int) -> List[Dict]:
        """使用adata获取交易日历"""
        if not self.adata_available:
            return []
        
        try:
            calendar_df = await asyncio.get_event_loop().run_in_executor(
                self.executor, self.adata.stock.info.trade_calendar, year
            )
            
            result = []
            for _, row in calendar_df.iterrows():
                result.append({
                    'trade_date': row.get('trade_date'),
                    'trade_status': row.get('trade_status'),
                    'day_week': row.get('day_week')
                })
            
            return result
            
        except Exception as e:
            self.logger.error(f"获取adata交易日历失败: {e}")
            return []
    
    async def validate_data_sources(self) -> Dict[str, bool]:
        """验证数据源可用性"""
        status = {
            'efinance': False,
            'adata': False
        }
        
        # 测试efinance
        try:
            test_indices = ['sh000300']  # 沪深300
            result = await self.get_index_quotes_efinance(test_indices)
            status['efinance'] = len(result) > 0
        except Exception as e:
            self.logger.error(f"efinance数据源测试失败: {e}")
        
        # 测试adata
        if self.adata_available:
            try:
                result = await self.get_index_quotes_adata(['sh000300'])
                status['adata'] = len(result) > 0
            except Exception as e:
                self.logger.error(f"adata数据源测试失败: {e}")
        
        return status
    
    async def get_index_history(self, index_code: str, days: int = 30) -> pd.DataFrame:
        """获取指数历史数据"""
        try:
            # 使用efinance获取历史数据
            from datetime import timedelta
            
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
            
            # efinance的股票历史数据接口
            history_df = await asyncio.get_event_loop().run_in_executor(
                self.executor, ef.stock.get_quote_history, index_code, start_date, end_date
            )
            
            return history_df
            
        except Exception as e:
            self.logger.error(f"获取指数历史数据失败: {e}")
            return pd.DataFrame()
    
    async def close(self):
        """关闭资源"""
        self.executor.shutdown(wait=True)
