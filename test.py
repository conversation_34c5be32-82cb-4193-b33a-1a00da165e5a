
import efinance as ef
import pandas as pd
from datetime import datetime, timedelta
import time
import os
import warnings

pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)
pd.set_option('display.width', 180)
warnings.filterwarnings('ignore') 



# --- 配置区域 ---
# 在这里配置你想要监控的期货品种及其对应的现货指数代码
# 期货代码: efinance 接受的期货代码, 如 IC2407, IM2409 等
# 现货代码: efinance 接受的指数代码, 如 sh000905 (中证500), sh000852 (中证1000)
FUTURES_CONFIG = {
    'IC': 'sh000905',  # 中证500股指期货 -> 中证500指数
    'IM': 'sh000852',  # 中证1000股指期货 -> 中证1000指数
    'IF': 'sh000300',  # 沪深300股指期货 -> 沪深300指数
    # 'ps': 'sh000000'
}

# 每年大致的交易日天数，用于年化计算
TRADING_DAYS_PER_YEAR = 243

# --- 核心功能函数 ---

def get_main_futures_contracts(product_codes: list) -> pd.DataFrame:
    """获取指定期货品种的主力合约实时行情"""
    print("正在获取期货主力合约数据...")
    all_futures_raw = ef.futures.get_realtime_quotes()
    print(all_futures_raw)
    # 筛选出我们配置中指定品种的、非0价格的、成交量最大的（通常是主力）合约
    main_contracts = []
    all_futures_raw['product_code'] = all_futures_raw['期货代码'].str.extract(r'([A-Za-z]+)')
    print(all_futures_raw)
    
    for code in product_codes:
        product_futures = all_futures_raw[all_futures_raw['product_code'] == code].copy()
        # product_futures = product_futures[product_futures['最新价'] > 0]
        if not product_futures.empty:
            main_contracts.append(product_futures.sort_values(by='成交量', ascending=False).iloc[0])
            
    if not main_contracts:
        return pd.DataFrame()
        
    df = pd.DataFrame(main_contracts)
    df = df[['期货代码', '最新价', '成交量', 'product_code']]
    df.set_index('product_code', inplace=True)
    print(f'get_main_futures_contracts result:')
    print(df)
    return df

def get_spot_index_quotes(index_codes: list) -> pd.DataFrame:
    """获取现货指数的实时行情"""
    print("正在获取现货指数数据...")
    df = ef.stock.get_realtime_quotes(index_codes)
    df = df[['代码', '名称', '最新价']]
    df.set_index('代码', inplace=True)
    return df

def get_remaining_trade_days(expiry_date_str: str) -> int:
    """计算从今天到指定到期日之间的交易日天数"""
    if not expiry_date_str or pd.isna(expiry_date_str):
        return 0
    today = datetime.now().strftime('%Y%m%d')
    # efinance 的到期日格式可能是 YYYY-MM-DD，需要统一
    expiry_date = expiry_date_str.replace('-', '')
    trade_days = ef.trade_calendar.get_trade_days(today, expiry_date)
    return len(trade_days)

def clear_screen():
    """清空控制台屏幕"""
    os.system('cls' if os.name == 'nt' else 'clear')

# --- 主程序 ---

def main():
    """主监控循环"""
    while True:
        try:
            clear_screen()
            print(f"--- 金融股指期货监控面板 (更新于: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}) ---")
            
            # 1. 获取期货数据
            product_codes = list(FUTURES_CONFIG.keys())
            futures_df = get_main_futures_contracts(product_codes)
            print(futures_df)

            
            if futures_df.empty:
                print("未能获取到期货数据，请检查网络或API。")
                time.sleep(30)
                continue

            # 2. 获取现货指数数据
            spot_codes = list(FUTURES_CONFIG.values())
            spot_df = get_spot_index_quotes(spot_codes)

            if spot_df.empty:
                print("未能获取到现货指数数据，请检查网络或API。")
                time.sleep(30)
                continue

            # 3. 组合数据并计算
            results = []
            for product_code, spot_code in FUTURES_CONFIG.items():
                if product_code not in futures_df.index or spot_code not in spot_df.index:
                    continue
                
                future_series = futures_df.loc[product_code]
                spot_series = spot_df.loc[spot_code]
                
                # 计算剩余交易日
                remaining_days = get_remaining_trade_days(future_series['到期日期'])
                
                # 计算基差和年化成本
                basis = future_series['最新价'] - spot_series['最新价']
                basis_ratio = (basis / spot_series['最新价']) * 100 if spot_series['最新价'] != 0 else 0
                
                annualized_cost = 0
                if remaining_days > 0:
                    # 年化成本 = (基差率 / 剩余天数) * 年交易天数
                    annualized_cost = (basis_ratio / remaining_days) * TRADING_DAYS_PER_YEAR

                results.append({
                    '期货品种': product_code,
                    '主力合约': future_series['合约代码'],
                    '期货价格': f"{future_series['最新价']:.2f}",
                    '现货指数': spot_series['名称'],
                    '现货价格': f"{spot_series['最新价']:.2f}",
                    '基差': f"{basis:.2f}",
                    '基差率(%)': f"{basis_ratio:.2f}",
                    '到期日': future_series['到期日期'],
                    '剩余交易日': remaining_days,
                    '年化基差成本(%)': f"{annualized_cost:.2f}"
                })

            # 4. 显示结果
            if not results:
                print("数据不足，无法生成监控面板。")
            else:
                result_df = pd.DataFrame(results)
                print(result_df.to_string(index=False))

            print("程序将在60秒后刷新...")
            time.sleep(60)

        except Exception as e:
            print(f"发生错误: {e}")
            print("将在60秒后重试...")
            time.sleep(60)

if __name__ == "__main__":
    main()