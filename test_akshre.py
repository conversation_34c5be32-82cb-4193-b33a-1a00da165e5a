#!/usr/bin/env python3
"""
测试AKShare期货接口
"""

import akshare as ak
import pandas as pd
import numpy as np

def test_futures_comm_info():
    """测试期货手续费与保证金信息"""
    print("=" * 50)
    print("测试期货手续费与保证金信息")
    print("=" * 50)

    try:
        # 获取中国金融期货交易所的期货信息
        df = ak.futures_comm_info(symbol="中国金融期货交易所")
        print(f"获取到 {len(df)} 条记录")
        print("\n数据列名:")
        print(df.columns.tolist())
        print("\n前5条数据:")
        print(df.head())

        # 筛选股指期货
        stock_index_futures = df[df['合约代码'].str.contains('IC|IF|IH|IM', na=False)]
        print(f"\n股指期货合约数量: {len(stock_index_futures)}")
        print("股指期货合约:")
        print(stock_index_futures[['合约代码', '合约名称', '现价']].head(10))

    except Exception as e:
        print(f"获取期货手续费信息失败: {e}")

def test_futures_zh_spot_em():
    """测试期货实时行情数据"""
    print("\n" + "=" * 50)
    print("测试期货实时行情数据")
    print("=" * 50)

    try:
        # 获取内盘期货实时行情
        df = ak.futures_zh_spot_em()
        print(f"获取到 {len(df)} 条记录")
        print("\n数据列名:")
        print(df.columns.tolist())
        print("\n前5条数据:")
        print(df.head())

        # 筛选股指期货
        stock_index_futures = df[df['代码'].str.contains('IC|IF|IH|IM', na=False)]
        print(f"\n股指期货行情数量: {len(stock_index_futures)}")
        print("股指期货行情:")
        print(stock_index_futures[['代码', '名称', '最新价', '涨跌幅']].head(10))

    except Exception as e:
        print(f"获取期货实时行情失败: {e}")

def test_futures_fees_info():
    """测试期货交易费用参照表"""
    print("\n" + "=" * 50)
    print("测试期货交易费用参照表")
    print("=" * 50)

    try:
        # 获取期货交易费用参照表
        df = ak.futures_fees_info()
        print(f"获取到 {len(df)} 条记录")
        print("\n数据列名:")
        print(df.columns.tolist())
        print("\n前5条数据:")
        print(df.head())

        # 筛选股指期货
        stock_index_futures = df[df['合约代码'].str.contains('IC|IF|IH|IM', na=False)]
        print(f"\n股指期货费用信息数量: {len(stock_index_futures)}")
        print("股指期货费用信息:")
        if not stock_index_futures.empty:
            print(stock_index_futures[['合约代码', '合约名称', '最新价', '成交量']].head(10))

    except Exception as e:
        print(f"获取期货交易费用信息失败: {e}")

if __name__ == "__main__":
    print("开始测试AKShare期货接口...")

    test_futures_comm_info()
    test_futures_zh_spot_em()
    test_futures_fees_info()

    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)