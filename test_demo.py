"""
测试演示脚本 - 展示改进后的基差成本矩阵功能
使用模拟数据演示合约信息的完整显示
"""
import pandas as pd
import warnings
from datetime import datetime, date
from src.models.data_models import FuturesContract, BasisData, CostMatrix
from src.analyzers.cost_matrix import CostMatrixGenerator

# 设置pandas显示选项
pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)
pd.set_option('display.width', 180)
warnings.filterwarnings('ignore')

def create_mock_contracts():
    """创建模拟合约数据"""
    contracts = [
        # IC 中证500股指期货
        FuturesContract(
            contract_code="IC2501",
            product_code="IC",
            contract_name="中证500股指2501",
            quote_id="IC2501",
            market_type="中金所",
            expiry_date=date(2025, 1, 17),
            is_main_contract=True
        ),
        FuturesContract(
            contract_code="IC2502",
            product_code="IC",
            contract_name="中证500股指2502",
            quote_id="IC2502",
            market_type="中金所",
            expiry_date=date(2025, 2, 21),
            is_main_contract=False
        ),
        FuturesContract(
            contract_code="IC2503",
            product_code="IC",
            contract_name="中证500股指2503",
            quote_id="IC2503",
            market_type="中金所",
            expiry_date=date(2025, 3, 21),
            is_main_contract=False
        ),
        
        # IF 沪深300股指期货
        FuturesContract(
            contract_code="IF2501",
            product_code="IF",
            contract_name="沪深300股指2501",
            quote_id="IF2501",
            market_type="中金所",
            expiry_date=date(2025, 1, 17),
            is_main_contract=True
        ),
        FuturesContract(
            contract_code="IF2502",
            product_code="IF",
            contract_name="沪深300股指2502",
            quote_id="IF2502",
            market_type="中金所",
            expiry_date=date(2025, 2, 21),
            is_main_contract=False
        ),
        
        # IH 上证50股指期货
        FuturesContract(
            contract_code="IH2501",
            product_code="IH",
            contract_name="上证50股指2501",
            quote_id="IH2501",
            market_type="中金所",
            expiry_date=date(2025, 1, 17),
            is_main_contract=True
        ),
        FuturesContract(
            contract_code="IH2502",
            product_code="IH",
            contract_name="上证50股指2502",
            quote_id="IH2502",
            market_type="中金所",
            expiry_date=date(2025, 2, 21),
            is_main_contract=False
        ),
        
        # IM 中证1000股指期货
        FuturesContract(
            contract_code="IM2501",
            product_code="IM",
            contract_name="中证1000股指2501",
            quote_id="IM2501",
            market_type="中金所",
            expiry_date=date(2025, 1, 17),
            is_main_contract=True
        ),
        FuturesContract(
            contract_code="IM2502",
            product_code="IM",
            contract_name="中证1000股指2502",
            quote_id="IM2502",
            market_type="中金所",
            expiry_date=date(2025, 2, 21),
            is_main_contract=False
        ),
    ]
    return contracts

def create_mock_basis_data():
    """创建模拟基差数据"""
    now = datetime.now()
    basis_costs = {
        "IC2501": BasisData(
            futures_code="IC2501",
            index_code="sh000905",
            timestamp=now,
            futures_price=5856.80,
            index_price=5895.93,
            basis=-39.13,
            basis_rate=-0.66,
            remaining_days=10,
            annualized_cost=-16.05
        ),
        "IC2502": BasisData(
            futures_code="IC2502",
            index_code="sh000905",
            timestamp=now,
            futures_price=5741.80,
            index_price=5895.93,
            basis=-154.13,
            basis_rate=-2.61,
            remaining_days=45,
            annualized_cost=-21.15
        ),
        "IC2503": BasisData(
            futures_code="IC2503",
            index_code="sh000905",
            timestamp=now,
            futures_price=5856.80,
            index_price=5895.93,
            basis=-39.13,
            basis_rate=-0.66,
            remaining_days=75,
            annualized_cost=-3.21
        ),
        
        "IF2501": BasisData(
            futures_code="IF2501",
            index_code="sh000300",
            timestamp=now,
            futures_price=3938.40,
            index_price=3958.86,
            basis=-20.46,
            basis_rate=-0.52,
            remaining_days=10,
            annualized_cost=-12.65
        ),
        "IF2502": BasisData(
            futures_code="IF2502",
            index_code="sh000300",
            timestamp=now,
            futures_price=3910.20,
            index_price=3958.86,
            basis=-48.66,
            basis_rate=-1.23,
            remaining_days=45,
            annualized_cost=-9.98
        ),
        
        "IH2501": BasisData(
            futures_code="IH2501",
            index_code="sh000016",
            timestamp=now,
            futures_price=2708.20,
            index_price=2725.74,
            basis=-17.54,
            basis_rate=-0.64,
            remaining_days=10,
            annualized_cost=-15.58
        ),
        "IH2502": BasisData(
            futures_code="IH2502",
            index_code="sh000016",
            timestamp=now,
            futures_price=2702.80,
            index_price=2725.74,
            basis=-22.94,
            basis_rate=-0.84,
            remaining_days=45,
            annualized_cost=-6.81
        ),
        
        "IM2501": BasisData(
            futures_code="IM2501",
            index_code="sh000852",
            timestamp=now,
            futures_price=6263.80,
            index_price=6320.79,
            basis=-56.99,
            basis_rate=-0.90,
            remaining_days=10,
            annualized_cost=-21.89
        ),
        "IM2502": BasisData(
            futures_code="IM2502",
            index_code="sh000852",
            timestamp=now,
            futures_price=6106.00,
            index_price=6320.79,
            basis=-214.79,
            basis_rate=-3.40,
            remaining_days=45,
            annualized_cost=-27.56
        ),
    }
    return basis_costs

def display_enhanced_matrix(contracts, basis_costs):
    """显示增强的成本矩阵"""
    matrix_generator = CostMatrixGenerator()

    # 创建基差成本矩阵DataFrame
    basis_df = matrix_generator.create_basis_matrix_dataframe(basis_costs, contracts)

    print("\n" + "="*120)
    print("【基差成本矩阵 - 改进版本】")
    print("="*120)

    if not basis_df.empty:
        # 添加到期日期和合约名称信息
        enhanced_data = []
        for _, row in basis_df.iterrows():
            contract_code = row['合约代码']

            # 获取合约详细信息
            contract = None
            for c in contracts:
                if c.contract_code == contract_code:
                    contract = c
                    break

            # 获取到期日期
            expiry_str = ""
            if contract and contract.expiry_date:
                expiry_str = contract.expiry_date.strftime("%m-%d")

            # 获取合约名称（简化显示）
            contract_name = ""
            if contract and contract.contract_name:
                # 提取关键信息
                if "中证500" in contract.contract_name:
                    contract_name = "中证500"
                elif "中证1000" in contract.contract_name:
                    contract_name = "中证1000"
                elif "沪深300" in contract.contract_name:
                    contract_name = "沪深300"
                elif "上证50" in contract.contract_name:
                    contract_name = "上证50"
                else:
                    contract_name = contract.contract_name[:6]

            enhanced_row = row.to_dict()
            enhanced_row['合约名称'] = contract_name
            enhanced_row['到期日期'] = expiry_str

            enhanced_data.append(enhanced_row)

        # 重新排列列顺序
        enhanced_df = pd.DataFrame(enhanced_data)
        column_order = [
            '品种', '合约代码', '合约名称', '合约月份', '是否主力',
            '期货价格', '现货价格', '基差', '基差率(%)',
            '剩余天数', '年化成本(%)', '到期日期', '更新时间'
        ]

        # 确保所有列都存在
        for col in column_order:
            if col not in enhanced_df.columns:
                enhanced_df[col] = ""

        enhanced_df = enhanced_df[column_order]

        print(enhanced_df.to_string(index=False))

        # 显示统计信息
        print(f"\n统计信息:")
        print(f"总合约数: {len(enhanced_df)}")

        # 按品种统计
        product_stats = enhanced_df.groupby('品种').size()
        for product, count in product_stats.items():
            main_count = len(enhanced_df[(enhanced_df['品种'] == product) & (enhanced_df['是否主力'] == '是')])
            print(f"{product}: {count}个合约 (主力: {main_count}个)")

        # 显示价格检查
        print(f"\n价格检查:")
        abnormal_count = 0
        for _, row in enhanced_df.iterrows():
            try:
                futures_price = float(row['期货价格'])
                spot_price = float(row['现货价格'])

                # 检查价格是否合理（期货价格应该在现货价格的合理范围内）
                if spot_price > 0:
                    price_ratio = futures_price / spot_price
                    if price_ratio < 0.8 or price_ratio > 1.2:
                        print(f"⚠️  {row['合约代码']}: 期货价格 {futures_price:.2f} vs 现货价格 {spot_price:.2f} (比率: {price_ratio:.3f})")
                        abnormal_count += 1
            except (ValueError, TypeError):
                continue

        if abnormal_count == 0:
            print("✅ 所有合约价格正常")

        # 显示基差分析
        print(f"\n基差分析:")
        for product in enhanced_df['品种'].unique():
            product_df = enhanced_df[enhanced_df['品种'] == product]
            avg_basis_rate = product_df['基差率(%)'].astype(float).mean()
            avg_cost = product_df['年化成本(%)'].astype(float).mean()
            print(f"{product}: 平均基差率 {avg_basis_rate:.2f}%, 平均年化成本 {avg_cost:.2f}%")

    else:
        print("暂无数据")

    print("="*120)

def main():
    """主函数"""
    print("金融股指期货监控系统 - 改进演示")
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 创建模拟数据
    contracts = create_mock_contracts()
    basis_costs = create_mock_basis_data()

    # 显示改进后的矩阵
    display_enhanced_matrix(contracts, basis_costs)

    print("\n改进要点:")
    print("1. ✅ 显示完整的合约信息（代码、名称、月份、到期日期）")
    print("2. ✅ 标识主力合约")
    print("3. ✅ 价格合理性检查")
    print("4. ✅ 按品种统计合约数量")
    print("5. ✅ 基差分析汇总")
    print("6. ✅ 更宽的显示格式，信息更完整")

if __name__ == "__main__":
    main()
