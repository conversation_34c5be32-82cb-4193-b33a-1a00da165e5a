"""
测试efinance的正确指数代码格式
"""
import efinance as ef

def test_index_codes():
    """测试不同的指数代码格式"""
    
    # 测试各种可能的格式
    test_formats = [
        # 沪深300
        ('000300', '沪深300'),
        ('sh000300', '沪深300'),
        ('1.000300', '沪深300'),
        ('SH000300', '沪深300'),
        
        # 中证500  
        ('000905', '中证500'),
        ('sh000905', '中证500'),
        ('1.000905', '中证500'),
        
        # 上证50
        ('000016', '上证50'),
        ('sh000016', '上证50'),
        ('1.000016', '上证50'),
        
        # 中证1000
        ('000852', '中证1000'),
        ('sh000852', '中证1000'),
        ('1.000852', '中证1000'),
    ]
    
    for code, name in test_formats:
        try:
            print(f"\n测试 {name} - 代码: {code}")
            df = ef.stock.get_quote_history(code, beg='20241201', end='20241231')
            
            if not df.empty:
                latest = df.iloc[-1]
                stock_name = latest.get('股票名称', 'N/A')
                close_price = latest.get('收盘', 0)
                
                print(f"  ✅ 成功 - 名称: {stock_name}, 收盘价: {close_price}")
                
                # 检查是否是正确的指数
                if name in stock_name or '300' in stock_name or '500' in stock_name or '1000' in stock_name or '50' in stock_name:
                    print(f"  🎯 这可能是正确的指数代码！")
            else:
                print(f"  ❌ 无数据")
                
        except Exception as e:
            print(f"  ❌ 失败: {e}")

if __name__ == "__main__":
    test_index_codes()
