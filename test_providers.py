"""
测试修复后的数据提供者
"""
import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.data_providers.efinance_provider import EfinanceProvider
from src.data_providers.index_provider import IndexDataProvider
from src.models.data_models import MonitoringConfig

async def test_efinance_provider():
    """测试efinance数据提供者"""
    print("=== 测试EfinanceProvider ===")
    
    provider = EfinanceProvider()
    
    try:
        # 测试获取期货合约
        print("\n1. 测试获取期货合约:")
        product_codes = ['IC', 'IM', 'IF', 'IH']
        contracts = await provider.get_futures_contracts(product_codes)
        
        print(f"获取到 {len(contracts)} 个合约:")
        for contract in contracts:
            print(f"  {contract.contract_code} - {contract.contract_name} - {contract.product_code}")
        
        if contracts:
            # 测试获取期货行情
            print("\n2. 测试获取期货行情:")
            contract_codes = [c.contract_code for c in contracts[:3]]  # 取前3个测试
            quotes = await provider.get_futures_quotes(contract_codes)
            
            print(f"获取到 {len(quotes)} 个行情:")
            for code, quote in quotes.items():
                print(f"  {code}: {quote.current_price}")
        
        # 测试获取交易日历
        print("\n3. 测试获取交易日历:")
        trading_days = await provider.get_trading_days('20241201', '20241231')
        print(f"获取到 {len(trading_days)} 个交易日")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await provider.close()

async def test_index_provider():
    """测试指数数据提供者"""
    print("\n=== 测试IndexDataProvider ===")
    
    provider = IndexDataProvider(use_adata_fallback=False)  # 暂时不使用adata
    
    try:
        # 测试获取指数行情
        print("\n1. 测试获取指数行情:")
        indices = ['sh000300', 'sh000905', 'sh000852', 'sh000016']
        quotes = await provider.get_index_quotes(indices)
        
        print(f"获取到 {len(quotes)} 个指数行情:")
        for code, quote in quotes.items():
            print(f"  {code} ({quote.index_name}): {quote.current_price}")
        
        # 测试数据源验证
        print("\n2. 测试数据源验证:")
        status = await provider.validate_data_sources()
        print(f"数据源状态: {status}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await provider.close()

async def test_integration():
    """测试集成"""
    print("\n=== 测试数据集成 ===")
    
    config = MonitoringConfig()
    efinance_provider = EfinanceProvider()
    index_provider = IndexDataProvider(use_adata_fallback=False)
    
    try:
        # 获取期货合约
        product_codes = list(config.futures_mapping.keys())
        contracts = await efinance_provider.get_futures_contracts(product_codes)
        print(f"获取到 {len(contracts)} 个期货合约")
        
        if contracts:
            # 获取期货行情
            contract_codes = [c.contract_code for c in contracts]
            futures_quotes = await efinance_provider.get_futures_quotes(contract_codes)
            print(f"获取到 {len(futures_quotes)} 个期货行情")
            
            # 获取指数行情
            index_codes = list(config.futures_mapping.values())
            index_quotes = await index_provider.get_index_quotes(index_codes)
            print(f"获取到 {len(index_quotes)} 个指数行情")
            
            # 显示匹配情况
            print("\n期货与指数匹配情况:")
            for contract in contracts:
                index_code = config.futures_mapping.get(contract.product_code)
                futures_price = futures_quotes.get(contract.contract_code)
                index_price = index_quotes.get(index_code)
                
                if futures_price and index_price:
                    basis = futures_price.current_price - index_price.current_price
                    print(f"  {contract.product_code}: 期货{futures_price.current_price:.2f} vs 指数{index_price.current_price:.2f}, 基差{basis:.2f}")
        
    except Exception as e:
        print(f"集成测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await efinance_provider.close()
        await index_provider.close()

async def main():
    """主函数"""
    await test_efinance_provider()
    await test_index_provider()
    await test_integration()

if __name__ == "__main__":
    asyncio.run(main())
