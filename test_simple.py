"""
简化的efinance API测试
"""
import efinance as ef
import pandas as pd

def test_stock_index_history():
    """测试股票指数历史数据"""
    print("=== 测试股票指数历史数据 ===")
    
    # 测试指数代码
    index_codes = ['000300', '000905', '000852', '000016']
    index_names = ['沪深300', '中证500', '中证1000', '上证50']
    
    for code, name in zip(index_codes, index_names):
        try:
            print(f"\n测试 {name} ({code}):")
            df = ef.stock.get_quote_history(code, beg='20241201', end='20241231')
            if not df.empty:
                latest = df.iloc[-1]
                print(f"  成功获取数据，最新收盘价: {latest['收盘']}")
                print(f"  日期: {latest['日期']}")
            else:
                print(f"  未获取到数据")
        except Exception as e:
            print(f"  失败: {e}")

def test_futures_stock_index():
    """测试股指期货数据"""
    print("\n=== 测试股指期货数据 ===")
    
    try:
        # 获取期货实时行情
        futures_df = ef.futures.get_realtime_quotes()
        print(f"获取到 {len(futures_df)} 个期货合约")
        
        # 筛选中金所合约
        cffex = futures_df[futures_df['市场类型'] == '中金所']
        print(f"中金所合约: {len(cffex)} 个")
        
        # 查找股指期货（IM系列）
        im_futures = cffex[cffex['期货代码'].str.contains('IM', na=False)]
        if not im_futures.empty:
            print(f"\nIM系列股指期货 ({len(im_futures)} 个):")
            print(im_futures[['期货代码', '期货名称', '最新价', '成交量']])
        
        # 查找其他可能的股指期货
        print(f"\n所有中金所期货代码:")
        unique_codes = cffex['期货代码'].unique()
        print(unique_codes)
        
        # 查找可能的IC、IF、IH
        for pattern in ['IC', 'IF', 'IH']:
            matches = cffex[cffex['期货代码'].str.contains(pattern, na=False)]
            if not matches.empty:
                print(f"\n{pattern}系列期货:")
                print(matches[['期货代码', '期货名称', '最新价']])
        
    except Exception as e:
        print(f"获取期货数据失败: {e}")

def test_futures_base_info():
    """测试期货基础信息"""
    print("\n=== 测试期货基础信息 ===")
    
    try:
        base_info = ef.futures.get_futures_base_info()
        cffex_info = base_info[base_info['市场类型'] == '中金所']
        
        print(f"中金所期货基础信息 ({len(cffex_info)} 个):")
        
        # 查找股指期货相关
        for pattern in ['IC', 'IM', 'IF', 'IH']:
            matches = cffex_info[cffex_info['期货代码'].str.contains(pattern, na=False)]
            if not matches.empty:
                print(f"\n{pattern}系列:")
                print(matches[['期货代码', '期货名称', '行情ID']])
        
    except Exception as e:
        print(f"获取期货基础信息失败: {e}")

if __name__ == "__main__":
    test_stock_index_history()
    test_futures_stock_index()
    test_futures_base_info()
